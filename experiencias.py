experiencias = list(set([
    "🎭 Ópera de Viena",
    "🎨 Museu do Louvre em Paris",
    "🎵 Concerto no Carnegie Hall",
    "🎪 Cirque du Soleil em Las Vegas",
    "🎬 Première de filme em Hollywood",
    "🎭 Teatro La Scala em Milão",
    "🎨 Museu Guggenheim em Bilbao",
    "🎵 Festival de Jazz em Montreux",
    "🎪 Cirque du Soleil em Dubai",
    "🎬 Première de filme em Cannes",
    "🎭 Teatro Bolshoi em Moscou",
    "🎨 Museu de Arte Moderna em Nova York",
    "🎵 Festival de Rock em Glastonbury",
    "🎪 Cirque du Soleil em Tóquio",
    "🎤 Show de Caetano Veloso em São Paulo",
    "🎸 Festival de Música de Paraty",
    "🎭 Espetáculo de teatro no Rio de Janeiro",
    "🎨 Exposição de arte contemporânea em Brasília",
    "🎪 Festival Internacional de Circo no Brasil",
    "🎬 Festival de Cinema de Gramado",
    "🎵 Concerto de Gilberto Gil em Salvador",
    "🎤 Show do Dennis DJ em Belo Horizonte",
    "🎵 Festival de Música de Inverno em Campos do Jordão",
    "🎭 Espetáculo de dança no Teatro Municipal do Rio",
    "🎨 Bienal de Arte de São Paulo",
    "🎪 Festival de Magia em São Paulo",
    "🎬 Pré-estreia de filme brasileiro em Brasília",
    "🎵 Show de Ivete Sangalo em Recife",
    "🎤 Concerto de MPB em Porto Alegre",
    "🎭 Teatro de Revista em São Paulo",
    "🎨 Exposição de Arte Brasileira no MASP",
    "🎪 Festival de Palhaços em Curitiba",
    "🎬 Festival de Cinema de Brasília",
    "🎵 Show de Roberto Carlos em Fortaleza",
    "🎤 Concerto de rock em São Paulo",
    "🎭 Espetáculo de Circo em São Paulo",
    "🎨 Exposição de Fotografia em Belo Horizonte",
    "🎪 Festival de Dança em Salvador",
    "🎬 Festival de Cinema de Tiradentes",
    "🎵 Show de Sandy e Junior em São Paulo",
    "🎤 Concerto de Jazz em São Paulo",
    "🎭 Espetáculo de Teatro Infantil em Brasília",
    "🎨 Feira de Arte e Artesanato em Olinda",
    "🎪 Festival de Música Sertaneja em Goiânia",
    "🎬 Festival de Cinema de Ouro Preto",
    "🎵 Show de Marisa Monte em Curitiba",
    "🎤 Concerto de Música Clássica em São Paulo",
    "🎭 Espetáculo de Stand-Up Comedy no Rio",
    "🎨 Exposição de Arte Popular em São Paulo",
    "🎪 Festival de Teatro de Rua em Salvador",
    "🎬 Festival de Cinema de Vitória",
    "🎵 Show de Djavan em Brasília",
    "🎤 Concerto de Música Eletrônica em São Paulo",
    "🎭 Espetáculo de Dança Contemporânea em Belo Horizonte",
    "🎨 Mostra de Cinema Brasileiro em São Paulo",
    "🎪 Festival de Circo em Porto Alegre",
    "🎬 Festival de Cinema de Recife",
    "🎵 Show de Alceu Valença em São Paulo",
    "🎤 Concerto de Música Popular Brasileira em Salvador",
    "🎭 Espetáculo de Teatro Musical em São Paulo",
    "🎨 Exposição de Arte Urbana em São Paulo",
    "🎪 Festival de Música Popular em Brasília",
    "🎬 Festival de Cinema de São Paulo",
    "🎵 Show de Lulu Santos em Belo Horizonte",
    "🎤 Concerto de Música Brasileira em Curitiba",
    "🎭 Espetáculo de Teatro Clássico no Rio",
    "🎨 Exposição de Pintura em São Paulo",
    "🎪 Festival de Dança Folclórica em Salvador",
    "🎬 Festival de Cinema de Natal",
    "🎵 Show de Vanessa da Mata em São Paulo",
    "🎤 Concerto de Música Instrumental em Brasília",
    "🎭 Espetáculo de Teatro Experimental em São Paulo",
    "🎨 Exposição de Arte Abstrata em Belo Horizonte",
    "🎪 Festival de Música Independente em Curitiba",
    "🎬 Festival de Cinema de Florianópolis",
    "🎵 Show de Fábio Jr. em São Paulo",
    "🎤 Concerto de Música de Câmara em Brasília",
    "🎭 Espetáculo de Teatro de Improviso em São Paulo",
    "🎨 Exposição de Arte Digital em São Paulo",
    "🎪 Festival de Música de Rua em Salvador",
    "🎬 Festival de Cinema de São João del-Rei",
    "🎵 Show de Adriana Calcanhotto em São Paulo",
    "🎤 Concerto de Música Popular em Porto Alegre",
    "🎭 Espetáculo de Teatro de Sombras em Brasília",
    "🎨 Exposição de Arte e Tecnologia em São Paulo",
    "🎪 Festival de Música de Verão em Salvador",
    "🎬 Festival de Cinema de São Paulo",
    "🎵 Show de Jota Quest em Belo Horizonte",
    "🎤 Concerto de Música de Raiz em São Paulo",
    "🎭 Espetáculo de Teatro de Fantoches em Salvador",
    "🎨 Exposição de Arte e Sustentabilidade em São Paulo",
    "🎪 Festival de Música de Outono em Curitiba",
    "🎬 Festival de Cinema de São Paulo",
    "🎵 Show de Roupa Nova em São Paulo",
    "🎤 Concerto de Música de Festa em Salvador",
    "🎭 Espetáculo de Teatro de Animação em São Paulo",
    "🎨 Exposição de Arte e Cultura Brasileira em Brasília",
    "🎪 Festival de Música de Primavera em São Paulo",
    "🎬 Festival de Cinema de São Paulo",
    "🎵 Tomorrowland na Bélgica",
    "🎵 Lollapalooza em Chicago",
    "🎵 Coachella na Califórnia",
    "🎵 Ultra Music Festival em Miami",
    "🎵 Glastonbury Festival no Reino Unido",
    "🎵 Electric Daisy Carnival em Las Vegas",
    "🎵 Ibiza Music Festival na Espanha",
    "🎵 Primavera Sound em Barcelona",
    "🎵 Rock in Rio no Brasil",
    "🎵 Sziget Festival na Hungria",
    "🎵 Reading and Leeds Festivals no Reino Unido",
    "🎵 Austin City Limits no Texas",
    "🎵 Montreux Jazz Festival na Suíça",
    "🎵 New Orleans Jazz & Heritage Festival",
    "🎵 Bonnaroo Music and Arts Festival no Tennessee",
    "🎵 Outside Lands Music and Arts Festival em San Francisco",
    "🏆 Campeonato Brasileiro de Futebol (Brasileirão) - Brasil",
    "🏆 UEFA Champions League - Europa",
    "🏆 Copa do Mundo da FIFA - Vários países",
    "🏆 Jogos Olímpicos - Vários países",
    "🏆 Fórmula 1 - Grande Prêmio de Mônaco - Mônaco",
    "🏆 Super Bowl - Estados Unidos",
    "🏆 Wimbledon - Torneio de Tênis - Reino Unido",
    "🏆 Tour de France - Ciclismo - França",
    "🏆 NBA Finals - Estados Unidos",
    "🏆 Copa América - América do Sul",
    "🏆 Eurocopa - Europa",
    "🏆 Final da Copa do Mundo de Rugby - Vários países",
    "🏆 Masters - Golfe - Estados Unidos",
    "🏆 US Open - Tênis - Estados Unidos",
    "🏆 Final da Copa Libertadores - América do Sul",
    "🏆 Final da Copa do Mundo de Futebol Feminino - Vários países",
    "🏄‍♂️ Campeonato Mundial de Surf - Vários locais",
    "🏍️ Campeonato Mundial de Motocross - Vários locais",
    "🚵‍♂️ Campeonato Mundial de Downhill - Vários locais",
    "🛹 X Games - Vários locais",
    "🏄‍♀️ ISA World Surfing Games - Vários locais",
    "🏍️ Red Bull X-Fighters - Vários locais",
    "🚵‍♀️ UCI Mountain Bike World Cup - Vários locais",
    "🛹 Street League Skateboarding - Vários locais",
    "🏄‍♂️ Vans Triple Crown of Surfing - Havai",
    "🏍️ MotoGP - Vários locais",
    "🚵‍♂️ Enduro World Series - Vários locais",
    "🛹 World Skateboarding Championships - Vários locais",
    "🏄‍♀️ Big Wave Awards - Havai",
    "🏍️ FIM Supercross World Championship - Vários locais",
    "🏅 Campeonato Mundial de Ginástica Artística - Vários locais",
    "🏅 Jogos Olímpicos de Ginástica Artística - Vários países",
    "🎮 Campeonato Mundial de Counter-Strike - Vários locais",
    "🎮 Campeonato Mundial de League of Legends - Vários locais",
    "🎮 Campeonato Mundial de Valorant - Vários locais",
    "🎮 The International (Dota 2) - Vários locais",
    "🎮 Overwatch League Finals - Vários locais",
    "🎮 Fortnite World Cup - Vários locais",
    "🎮 ESL One (Dota 2) - Vários locais",
    "🎮 Call of Duty League Championship - Vários locais",
    "🎮 Apex Legends Global Series - Vários locais",
    "🌌 Visitar as Pirâmides de Gizé - Egito",
    "🏔️ Escalar o Monte Everest - Nepal",
    "🌍 Fazer um safári no Serengeti - Tanzânia",
    "🏖️ Relaxar nas Maldivas - Oceano Índico",
    "🏰 Visitar o Castelo de Neuschwanstein - Alemanha",
    "🗼 Subir na Torre Eiffel - França",
    "🏞️ Caminhar na Trilha Inca - Peru",
    "🌊 Mergulhar na Grande Barreira de Corais - Austrália",
    "🏙️ Explorar Nova York - EUA",
    "🌅 Assistir ao nascer do sol em Machu Picchu - Peru",
    "🍷 Fazer uma degustação de vinhos na Toscana - Itália",
    "🕌 Visitar a Mesquita Sheikh Zayed - Emirados Árabes",
    "🌉 Caminhar pela Ponte Golden Gate - EUA",
    "🦁 Fazer um safári no Kruger National Park - África do Sul",
    "🏖️ Visitar a Praia de Whitehaven - Austrália",
    "🏔️ Fazer uma trilha no Parque Nacional Torres del Paine - Chile",
    "🏞️ Explorar o Parque Nacional Yellowstone - EUA",
    "🌌 Ver a Aurora Boreal na Islândia",
    "🏰 Visitar o Taj Mahal - Índia",
    "🏖️ Relaxar em Bora Bora - Polinésia Francesa",
    "🌄 Fazer a trilha do Monte Roraima - Brasil/Venezuela",
    "🏙️ Conhecer Tóquio - Japão",
    "🏕️ Acampar no Parque Nacional Yosemite - EUA",
    "🌊 Surf nas praias de Bali - Indonésia",
    "🏖️ Explorar as Ilhas Gregas - Grécia",
    "🌍 Fazer um cruzeiro pelo Rio Amazonas - Brasil",
    "🗽 Visitar a Estátua da Liberdade - EUA",
    "🏔️ Fazer esqui nos Alpes - Europa",
    "🌄 Visitar o Grand Canyon - EUA",
    "🏞️ Explorar a Floresta Amazônica - Brasil",
    "🏰 Visitar o Palácio de Buckingham - Reino Unido",
    "🏖️ Relaxar nas praias de Cancun - México",
    "🗼 Subir na Torre de Pisa - Itália",
    "🏕️ Fazer uma trilha no Parque Nacional Banff - Canadá",
    "🌅 Assistir ao pôr do sol em Santorini - Grécia",
    "🏖️ Mergulhar em Cozumel - México",
    "🌊 Fazer rafting no Rio Colorado - EUA",
    "🏔️ Escalar o Monte Kilimanjaro - Tanzânia",
    "🏞️ Visitar o Parque Nacional de Zion - EUA",
    "🌌 Ver as Estrelas no Deserto do Atacama - Chile",
    "🏞️ Fazer uma trilha no Parque Nacional de Yosemite - EUA",
    "🏖️ Relaxar em Seychelles - Oceano Índico",
    "🏔️ Fazer um cruzeiro pelos Fiordes da Noruega",
    "🏰 Visitar o Castelo de Edimburgo - Escócia",
    "🌊 Fazer snorkeling nas Ilhas Maldivas - Oceano Índico",
    "🏖️ Explorar a Costa Amalfitana - Itália",
    "🗼 Visitar o Coliseu - Itália",
    "🏞️ Fazer um passeio de barco nas Cataratas do Iguaçu - Brasil/Argentina",
    "🌌 Ver as Luzes do Norte na Noruega",
    "🏔️ Fazer uma trilha no Parque Nacional de Glacier - EUA",
    "🏖️ Relaxar em Phuket - Tailândia",
    "🏰 Visitar o Castelo de Chambord - França",
    "🌍 Fazer um cruzeiro pelo Mediterrâneo",
    "🏞️ Explorar o Parque Nacional de Zion - EUA",
    "🏔️ Escalar o Monte Fuji - Japão",
    "🏖️ Mergulhar nas Ilhas Galápagos - Equador",
    "🌊 Fazer kitesurf em Tarifa - Espanha",
    "🏰 Visitar o Castelo de Hohenzollern - Alemanha",
    "🏞️ Fazer uma trilha no Parque Nacional de Torres del Paine - Chile",
    "🌌 Ver as Estrelas no Deserto de Atacama - Chile",
    "🏖️ Relaxar nas praias de Santorini - Grécia",
    "🏔️ Fazer um cruzeiro pelos Fiordes da Noruega",
    "🏰 Visitar o Palácio de Versalhes - França",
    "🏖️ Explorar as Ilhas Fiji - Pacífico Sul",
    "🌍 Fazer um safári no Parque Nacional de Serengeti - Tanzânia",
    "🏞️ Explorar o Parque Nacional de Yosemite - EUA",
    "🏔️ Escalar o Monte Aconcágua - Argentina",
    "🏖️ Relaxar nas praias de Bora Bora - Polinésia Francesa",
    "🌌 Ver a Aurora Boreal na Islândia",
    "🏰 Visitar o Castelo de Neuschwanstein - Alemanha",
    "🏞️ Fazer uma trilha no Parque Nacional de Banff - Canadá",
]))
