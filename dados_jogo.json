{"nome_usuario": "<PERSON><PERSON>", "saldo": 36069857.0, "nivel": 13, "xp": 5355, "xp_proximo_nivel": 12921, "ultima_loteria": "2025-03-28", "modalidade_sorteada": "Lotofácil", "conquistas": {"manifestador_iniciante": {"nome": "Manifestador Iniciante", "badge": "🥉", "descricao": "Alcance R$ 100.000", "recompensa": 10000, "desbloqueada": true}, "manifestador_expert": {"nome": "Manifestador Expert", "badge": "🥈", "descricao": "Alcance R$ 1.000.000", "recompensa": 50000, "desbloqueada": true}, "manifestador_mestre": {"nome": "Manifestador Mestre", "badge": "🥇", "descricao": "Alcance R$ 10.000.000", "recompensa": 100000, "desbloqueada": true}, "manifestador_elite": {"nome": "Manifestador Elite", "badge": "👑", "descricao": "Alcance R$ 100.000.000", "recompensa": 500000, "desbloqueada": false}, "gratidao_iniciante": {"nome": "Gratidão Iniciante", "badge": "🙏", "descricao": "Registre 10 gratidões", "recompensa": 5000, "desbloqueada": false}, "gratidao_intermediario": {"nome": "Gratidão Intermediário", "badge": "🙏🙏", "descricao": "Registre 50 gratidões", "recompensa": 10000, "desbloqueada": false}, "gratidao_avancado": {"nome": "<PERSON><PERSON><PERSON><PERSON>", "badge": "🙏🙏🙏", "descricao": "Registre 100 gratidões", "recompensa": 25000, "desbloqueada": false}, "gratidao_mestre": {"nome": "Me<PERSON><PERSON> da Gratidão", "badge": "🌟", "descricao": "Registre 500 gratidões", "recompensa": 100000, "desbloqueada": false}, "visualizador_iniciante": {"nome": "Visualizador <PERSON>", "badge": "👁️", "descricao": "Complete 10 visualizações", "recompensa": 5000, "desbloqueada": false}, "visualizador_intermediario": {"nome": "Visualizador Intermediário", "badge": "👁️👁️", "descricao": "Complete 50 visualizações", "recompensa": 10000, "desbloqueada": false}, "visualizador_avancado": {"nome": "<PERSON><PERSON><PERSON>", "badge": "👁️👁️👁️", "descricao": "Complete 100 visualizações", "recompensa": 25000, "desbloqueada": false}, "visualizador_pro": {"nome": "Visualizador Profissional", "badge": "💎", "descricao": "Complete 500 visualizações", "recompensa": 200000, "desbloqueada": false}, "meditador_iniciante": {"nome": "Meditador Iniciante", "badge": "🧘", "descricao": "Medite por 1 hora no total", "recompensa": 5000, "desbloqueada": false}, "meditador_intermediario": {"nome": "Meditador Intermediário", "badge": "🧘🧘", "descricao": "Medite por 5 horas no total", "recompensa": 10000, "desbloqueada": false}, "meditador_avancado": {"nome": "Meditador Avançado", "badge": "🧘🧘🧘", "descricao": "Medite por 10 horas no total", "recompensa": 25000, "desbloqueada": false}, "mestre_zen": {"nome": "Mestre Zen", "badge": "👑", "descricao": "Medite por 50 horas no total", "recompensa": 500000, "desbloqueada": false}, "investidor_iniciante": {"nome": "Investidor <PERSON>ici<PERSON>", "badge": "💰", "descricao": "Faça 5 investimentos", "recompensa": 5000, "desbloqueada": true}, "investidor_intermediario": {"nome": "Investidor Intermediário", "badge": "💰💰", "descricao": "Faça 20 investimentos", "recompensa": 10000, "desbloqueada": false}, "investidor_avancado": {"nome": "Investi<PERSON>", "badge": "💰💰💰", "descricao": "Faça 50 investimentos", "recompensa": 25000, "desbloqueada": false}, "investidor_mestre": {"nome": "Mestre dos Investimentos", "badge": "💎", "descricao": "Faça 100 investimentos", "recompensa": 100000, "desbloqueada": false}, "nivel_5": {"nome": "Iniciante Promissor", "badge": "⭐", "descricao": "Alcance o nível 5", "recompensa": 5000, "desbloqueada": true}, "nivel_10": {"nome": "Manifestador em Ascensão", "badge": "⭐⭐", "descricao": "Alcance o nível 10", "recompensa": 10000, "desbloqueada": true}, "nivel_20": {"nome": "Manifestador Experiente", "badge": "⭐⭐⭐", "descricao": "Alcance o nível 20", "recompensa": 25000, "desbloqueada": false}, "nivel_50": {"nome": "Manifestador Elite", "badge": "👑", "descricao": "Alcance o nível 50", "recompensa": 100000, "desbloqueada": false}, "surpresa_iniciante": {"nome": "Surpresas Iniciais", "badge": "🎁", "descricao": "Receba 10 surpresas", "recompensa": 5000, "desbloqueada": true}, "surpresa_intermediario": {"nome": "Surpresas Intermediárias", "badge": "🎁🎁", "descricao": "Receba 50 surpresas", "recompensa": 10000, "desbloqueada": true}, "surpresa_avancado": {"nome": "Surpresas Avançadas", "badge": "🎁🎁🎁", "descricao": "Receba 100 surpresas", "recompensa": 25000, "desbloqueada": true}, "surpresa_mestre": {"nome": "Mestre das Surpresas", "badge": "💫", "descricao": "Receba 500 surpresas", "recompensa": 100000, "desbloqueada": false}, "presente_iniciante": {"nome": "Presentes Iniciais", "badge": "🎀", "descricao": "Receba 10 presentes", "recompensa": 5000, "desbloqueada": true}, "presente_intermediario": {"nome": "Presentes Intermediá<PERSON>s", "badge": "🎀🎀", "descricao": "Receba 50 presentes", "recompensa": 10000, "desbloqueada": false}, "presente_avancado": {"nome": "Presentes <PERSON>", "badge": "🎀🎀🎀", "descricao": "Receba 100 presentes", "recompensa": 25000, "desbloqueada": false}, "presente_mestre": {"nome": "Mestre dos Presentes", "badge": "✨", "descricao": "Receba 500 presentes", "recompensa": 100000, "desbloqueada": false}, "afirmacao_iniciante": {"nome": "Afirmações Iniciais", "badge": "💫", "descricao": "Ative 10 afirmações", "recompensa": 5000, "desbloqueada": true}, "afirmacao_intermediario": {"nome": "Afirmações Intermediárias", "badge": "💫💫", "descricao": "Ative 50 afirmações", "recompensa": 10000, "desbloqueada": false}, "afirmacao_avancado": {"nome": "Afirmações Avançadas", "badge": "💫💫💫", "descricao": "Ative 100 afirmações", "recompensa": 25000, "desbloqueada": false}, "afirmacao_mestre": {"nome": "Mestre das Afirmações", "badge": "🌟", "descricao": "Ative 500 afirmações", "recompensa": 100000, "desbloqueada": false}}, "visualizacoes_hoje": 0, "total_meditado": 0, "tempo_total_meditado": 0, "investimentos_feitos": 6, "gratidoes_registradas": 0, "afirmacoes_ativadas": 47, "ultimo_investimento": "2025-03-27 03:06:23", "investimentos_ativos": {}, "moedas_especiais": 96, "itens_comprados": []}