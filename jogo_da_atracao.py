import tkinter as tk
from tkinter import ttk, messagebox
import json
from datetime import datetime
import random
import winsound  # Adicionar import do winsound
from carros import carros
from presentes import presentes_universo
from experiencias import experiencias
from destinos import destinos

class JogoLeiAtracao(tk.Tk):
    def __init__(self):
        # Inicialização da janela principal
        super().__init__()
        self.title("✨ Universo Abundante ✨")

        # Sistema de detecção de resolução e configuração responsiva
        self.detectar_resolucao()
        self.configurar_dimensoes_responsivas()
        
        # Lista de presentes do universo
        self.presentes_universo = presentes_universo
        
        # Lista de surpresas
        self.surpresas = {
            'carros': carros,
            'experiencias': experiencias,
            'destinos': destinos
        }
        
        # Sistema de cores moderno com gradientes e efeitos
        self.cores = {
            # Cores base modernizadas
            'bg_principal': '#0d1117',      # Fundo principal GitHub dark
            'bg_secundario': '#161b22',     # Fundo secundário
            'bg_terciario': '#21262d',      # Fundo terciário
            'card': '#21262d',              # Cards modernos
            'card_hover': '#30363d',        # Hover suave
            'card_active': '#373e47',       # Estado ativo

            # Textos com melhor contraste
            'texto': '#f0f6fc',             # Texto principal claro
            'texto_secundario': '#8b949e',  # Texto secundário
            'texto_muted': '#656d76',       # Texto esmaecido

            # Cores de destaque modernas
            'destaque': '#f78166',          # Coral moderno
            'destaque_hover': '#ff8a73',    # Hover coral
            'destaque_active': '#e5704b',   # Ativo coral

            # Sistema de cores semânticas
            'sucesso': '#3fb950',           # Verde GitHub
            'sucesso_hover': '#56d364',     # Verde hover
            'sucesso_bg': '#0d1117',        # Fundo sucesso

            'erro': '#f85149',              # Vermelho moderno
            'erro_hover': '#ff6b6b',        # Vermelho hover
            'erro_bg': '#0d1117',           # Fundo erro

            'aviso': '#d29922',             # Amarelo moderno
            'aviso_hover': '#e2a336',       # Amarelo hover
            'aviso_bg': '#0d1117',          # Fundo aviso

            'info': '#58a6ff',              # Azul moderno
            'info_hover': '#79c0ff',        # Azul hover
            'info_bg': '#0d1117',           # Fundo info

            # Botões modernos
            'botao': '#238636',             # Verde GitHub
            'botao_hover': '#2ea043',       # Verde hover
            'botao_active': '#1a7f37',      # Verde ativo
            'botao_secundario': '#21262d',  # Botão secundário
            'botao_secundario_hover': '#30363d', # Secundário hover
            'texto_botao': '#ffffff',       # Texto botão

            # Bordas e separadores
            'borda': '#30363d',             # Borda padrão
            'borda_hover': '#484f58',       # Borda hover
            'separador': '#21262d',         # Separadores

            # Sistema premium melhorado
            'dourado': '#ffd700',           # Dourado vibrante
            'dourado_escuro': '#b8860b',    # Dourado escuro
            'dourado_claro': '#ffed4e',     # Dourado claro
            'premium_bg': '#1c1917',        # Fundo premium
            'premium_border': '#a16207',    # Borda premium

            # Cores especiais
            'prata': '#e5e7eb',             # Prata moderna
            'bronze': '#d97706',            # Bronze moderno
            'diamante': '#06b6d4',          # Diamante cyan

            # Barras de progresso
            'progresso': '#3fb950',         # Verde progresso
            'progresso_bg': '#21262d',      # Fundo progresso
            'progresso_border': '#30363d',  # Borda progresso

            # Efeitos visuais (cores sólidas para compatibilidade)
            'sombra': '#000000',            # Sombra suave
            'sombra_forte': '#000000',      # Sombra forte
            'overlay': '#000000',           # Overlay
            'glow': '#58a6ff',              # Brilho azul
            'glow_sucesso': '#3fb950',      # Brilho verde
            'glow_erro': '#f85149',         # Brilho vermelho
        }

        # Desativar som dos botões
        self.option_add('*Button.highlightBackground', self.cores['bg_principal'])
        self.option_add('*Button.highlightColor', self.cores['bg_principal'])
        self.option_add('*Button.takeFocus', False)
        
        # Fontes melhoradas com mais variedade e tamanhos
        self.fontes = {
            'titulo_principal': ('Segoe UI', 24, 'bold'),
            'titulo': ('Segoe UI', 20, 'bold'),
            'subtitulo': ('Segoe UI', 16, 'bold'),
            'texto': ('Segoe UI', 12),
            'texto_pequeno': ('Segoe UI', 10),
            'numeros_grandes': ('Segoe UI', 32, 'bold'),
            'numeros': ('Segoe UI', 28, 'bold'),
            'numeros_pequenos': ('Segoe UI', 16, 'bold'),
            'botao': ('Segoe UI', 12, 'bold'),
            'botao_pequeno': ('Segoe UI', 10),
            'emoji': ('Segoe UI Emoji', 16),
            'emoji_grande': ('Segoe UI Emoji', 24)
        }
        
        # Variáveis do usuário
        self.nome_usuario = ""
        self.saldo = 0
        self.nivel = 1
        self.xp = 0
        self.xp_proximo_nivel = 100
        self.nivel_label = None  # Referência para o label do nível
        self.progress = None     # Referência para a barra de progresso

        # Sistema de efeitos visuais modernos
        self.efeitos_ativos = []
        self.animacoes_ativas = []

        # Configurações de animação melhoradas
        self.config_animacao = {
            'duracao_fade': 300,
            'duracao_slide': 250,
            'duracao_bounce': 400,
            'duracao_pulse': 1000,
            'duracao_glow': 2000,
            'fps': 60,
            'easing': 'ease_out'
        }

        # Sistema de temas modernos
        self.tema_atual = 'escuro_moderno'
        self.temas_disponiveis = {
            'escuro_moderno': self.cores,
            'claro_moderno': {},
            'alto_contraste': {},
            'neon': {}
        }
        
        # Inicialização de contadores
        self.visualizacoes_hoje = 0
        self.total_meditado = 0
        self.tempo_total_meditado = 0
        self.investimentos_feitos = 0
        self.gratidoes_registradas = 0
        self.afirmacoes_ativadas = 0

        # Sistema de moedas especiais para a loja
        self.moedas_especiais = 0
        self.itens_comprados = []

        # Sistema de zoom avançado
        self.zoom_level = 1.0
        self.zoom_min = 0.5
        self.zoom_max = 2.0
        self.zoom_step = 0.1
        self.zoom_preferences = {}

        # Lista de notificações ativas
        self.notificacoes_ativas = []
        
        # Cores das conquistas
        self.cores_conquistas = {
            'bronze': {
                'bg': '#CD7F32',
                'texto': '#FFFFFF',
                'borda': '#B87333',
                'brilho': '#FFD700'
            },
            'prata': {
                'bg': '#C0C0C0',
                'texto': '#FFFFFF',
                'borda': '#A8A8A8',
                'brilho': '#E8E8E8'
            },
            'ouro': {
                'bg': '#FFD700',
                'texto': '#000000',
                'borda': '#DAA520',
                'brilho': '#FFF8DC'
            },
            'platina': {
                'bg': '#E5E4E2',
                'texto': '#000000',
                'borda': '#C0C0C0',
                'brilho': '#F5F5F5'
            },
            'diamante': {
                'bg': '#B9F2FF',
                'texto': '#000000',
                'borda': '#00CED1',
                'brilho': '#E0FFFF'
            }
        }

        # Efeitos visuais para conquistas
        self.efeitos_conquistas = {
            'bronze': {
                'icone': "🏆",
                'particulas': "✨",
                'brilho': "💫"
            },
            'prata': {
                'icone': "⭐",
                'particulas': "🌟",
                'brilho': "✨"
            },
            'ouro': {
                'icone': "👑",
                'particulas': "💫",
                'brilho': "🌟"
            },
            'platina': {
                'icone': "💎",
                'particulas': "✨",
                'brilho': "💫"
            },
            'diamante': {
                'icone': "💫",
                'particulas': "🌟",
                'brilho': "✨"
            }
        }
        
        # Badges personalizados para conquistas
        self.badges = {
            'bronze': """
            🏆
            🥉
            """,
            'prata': """
            ⭐
            🥈
            """,
            'ouro': """
            👑
            🥇
            """,
            'diamante': """
            💫
            💎
            """,
            'especial': """
            🌟
            💫
            """
        }
        
        # Sistema de conquistas
        self.conquistas = {
            # Conquistas de Manifestação Financeira
            'manifestador_iniciante': {
                'nome': "Manifestador Iniciante",
                'badge': "🥉",
                'descricao': "Alcance R$ 100.000",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'manifestador_expert': {
                'nome': "Manifestador Expert",
                'badge': "🥈",
                'descricao': "Alcance R$ 1.000.000",
                'recompensa': 50000,
                'desbloqueada': False
            },
            'manifestador_mestre': {
                'nome': "Manifestador Mestre",
                'badge': "🥇",
                'descricao': "Alcance R$ 10.000.000",
                'recompensa': 100000,
                'desbloqueada': False
            },
            'manifestador_elite': {
                'nome': "Manifestador Elite",
                'badge': "👑",
                'descricao': "Alcance R$ 100.000.000",
                'recompensa': 500000,
                'desbloqueada': False
            },
            
            # Conquistas de Gratidão
            'gratidao_iniciante': {
                'nome': "Gratidão Iniciante",
                'badge': "🙏",
                'descricao': "Registre 10 gratidões",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'gratidao_intermediario': {
                'nome': "Gratidão Intermediário",
                'badge': "🙏🙏",
                'descricao': "Registre 50 gratidões",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'gratidao_avancado': {
                'nome': "Gratidão Avançado",
                'badge': "🙏🙏🙏",
                'descricao': "Registre 100 gratidões",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'gratidao_mestre': {
                'nome': "Mestre da Gratidão",
                'badge': "🌟",
                'descricao': "Registre 500 gratidões",
                'recompensa': 100000,
                'desbloqueada': False
            },
            
            # Conquistas de Visualização
            'visualizador_iniciante': {
                'nome': "Visualizador Iniciante",
                'badge': "👁️",
                'descricao': "Complete 10 visualizações",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'visualizador_intermediario': {
                'nome': "Visualizador Intermediário",
                'badge': "👁️👁️",
                'descricao': "Complete 50 visualizações",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'visualizador_avancado': {
                'nome': "Visualizador Avançado",
                'badge': "👁️👁️👁️",
                'descricao': "Complete 100 visualizações",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'visualizador_pro': {
                'nome': "Visualizador Profissional",
                'badge': "💎",
                'descricao': "Complete 500 visualizações",
                'recompensa': 100000,
                'desbloqueada': False
            },
            
            # Conquistas de Meditação
            'meditador_iniciante': {
                'nome': "Meditador Iniciante",
                'badge': "🧘",
                'descricao': "Medite por 1 hora no total",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'meditador_intermediario': {
                'nome': "Meditador Intermediário",
                'badge': "🧘🧘",
                'descricao': "Medite por 5 horas no total",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'meditador_avancado': {
                'nome': "Meditador Avançado",
                'badge': "🧘🧘🧘",
                'descricao': "Medite por 10 horas no total",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'mestre_zen': {
                'nome': "Mestre Zen",
                'badge': "👑",
                'descricao': "Medite por 50 horas no total",
                'recompensa': 100000,
                'desbloqueada': False
            },
            
            # Conquistas de Investimentos
            'investidor_iniciante': {
                'nome': "Investidor Iniciante",
                'badge': "💰",
                'descricao': "Faça 5 investimentos",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'investidor_intermediario': {
                'nome': "Investidor Intermediário",
                'badge': "💰💰",
                'descricao': "Faça 20 investimentos",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'investidor_avancado': {
                'nome': "Investidor Avançado",
                'badge': "💰💰💰",
                'descricao': "Faça 50 investimentos",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'investidor_mestre': {
                'nome': "Mestre dos Investimentos",
                'badge': "💎",
                'descricao': "Faça 100 investimentos",
                'recompensa': 100000,
                'desbloqueada': False
            },
            
            # Conquistas de Nível
            'nivel_5': {
                'nome': "Iniciante Promissor",
                'badge': "⭐",
                'descricao': "Alcance o nível 5",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'nivel_10': {
                'nome': "Manifestador em Ascensão",
                'badge': "⭐⭐",
                'descricao': "Alcance o nível 10",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'nivel_20': {
                'nome': "Manifestador Experiente",
                'badge': "⭐⭐⭐",
                'descricao': "Alcance o nível 20",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'nivel_50': {
                'nome': "Manifestador Elite",
                'badge': "👑",
                'descricao': "Alcance o nível 50",
                'recompensa': 100000,
                'desbloqueada': False
            },
            
            # Conquistas de Surpresas
            'surpresa_iniciante': {
                'nome': "Surpresas Iniciais",
                'badge': "🎁",
                'descricao': "Receba 10 surpresas",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'surpresa_intermediario': {
                'nome': "Surpresas Intermediárias",
                'badge': "🎁🎁",
                'descricao': "Receba 50 surpresas",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'surpresa_avancado': {
                'nome': "Surpresas Avançadas",
                'badge': "🎁🎁🎁",
                'descricao': "Receba 100 surpresas",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'surpresa_mestre': {
                'nome': "Mestre das Surpresas",
                'badge': "💫",
                'descricao': "Receba 500 surpresas",
                'recompensa': 100000,
                'desbloqueada': False
            },
            
            # Conquistas de Presentes
            'presente_iniciante': {
                'nome': "Presentes Iniciais",
                'badge': "🎀",
                'descricao': "Receba 10 presentes",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'presente_intermediario': {
                'nome': "Presentes Intermediários",
                'badge': "🎀🎀",
                'descricao': "Receba 50 presentes",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'presente_avancado': {
                'nome': "Presentes Avançados",
                'badge': "🎀🎀🎀",
                'descricao': "Receba 100 presentes",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'presente_mestre': {
                'nome': "Mestre dos Presentes",
                'badge': "✨",
                'descricao': "Receba 500 presentes",
                'recompensa': 100000,
                'desbloqueada': False
            },
            
            # Conquistas de Afirmações
            'afirmacao_iniciante': {
                'nome': "Afirmações Iniciais",
                'badge': "💫",
                'descricao': "Ative 10 afirmações",
                'recompensa': 5000,
                'desbloqueada': False
            },
            'afirmacao_intermediario': {
                'nome': "Afirmações Intermediárias",
                'badge': "💫💫",
                'descricao': "Ative 50 afirmações",
                'recompensa': 10000,
                'desbloqueada': False
            },
            'afirmacao_avancado': {
                'nome': "Afirmações Avançadas",
                'badge': "💫💫💫",
                'descricao': "Ative 100 afirmações",
                'recompensa': 25000,
                'desbloqueada': False
            },
            'afirmacao_mestre': {
                'nome': "Mestre das Afirmações",
                'badge': "🌟",
                'descricao': "Ative 500 afirmações",
                'recompensa': 100000,
                'desbloqueada': False
            }
        }
        
        # Controle de loteria
        self.ultima_loteria = None  # Data do último sorteio
        self.modalidade_sorteada = None  # Modalidade já sorteada hoje
        self.loteria_sorteada = False  # Flag para evitar sorteios múltiplos
        
        # Adicionar controle de investimentos
        self.ultimo_investimento = None  # Data do último investimento
        self.investimentos_ativos = {}  # Dicionário para controlar investimentos ativos
        
        # Modalidades de loteria
        self.modalidades_loteria = {
            'Mega-Sena': {'quantidade': 6, 'max_numero': 60},
            'Quina': {'quantidade': 5, 'max_numero': 80},
            'Lotofácil': {'quantidade': 15, 'max_numero': 25},
            'Lotomania': {'quantidade': 50, 'max_numero': 100},
            'Timemania': {'quantidade': 10, 'max_numero': 80},
            'Dupla Sena': {'quantidade': 6, 'max_numero': 50},
            'Dia de Sorte': {'quantidade': 7, 'max_numero': 31},
            'Super Sete': {'quantidade': 7, 'max_numero': 9}
        }
        
        # Verificar primeiro acesso
        if not self.carregar_dados():
            self.primeiro_acesso()
        else:
            self.setup_interface()
            self.carregar_logs()  # Carregar logs após setup da interface
            self.verificar_retornos_investimentos()  # Verificar retornos após interface criada

    def carregar_dados(self):
        try:
            with open('dados_jogo.json', 'r') as f:
                dados = json.load(f)
                self.nome_usuario = dados.get('nome_usuario', '')
                self.saldo = dados.get('saldo', 0)
                self.nivel = dados.get('nivel', 1)
                self.xp = dados.get('xp', 0)
                self.xp_proximo_nivel = dados.get('xp_proximo_nivel', 100)
                
                # Carregar dados da loteria
                ultima_loteria = dados.get('ultima_loteria')
                self.ultima_loteria = datetime.strptime(ultima_loteria, '%Y-%m-%d').date() if ultima_loteria else None
                self.modalidade_sorteada = dados.get('modalidade_sorteada')
                
                # Atualizar conquistas mantendo as chaves necessárias
                if 'conquistas' in dados:
                    for conquista_id, conquista in dados['conquistas'].items():
                        if conquista_id in self.conquistas:
                            self.conquistas[conquista_id].update({
                                'desbloqueada': conquista.get('desbloqueada', False),
                                'recompensa': conquista.get('recompensa', self.conquistas[conquista_id]['recompensa'])
                            })
                
                self.visualizacoes_hoje = dados.get('visualizacoes_hoje', 0)
                self.total_meditado = dados.get('total_meditado', 0)
                self.tempo_total_meditado = dados.get('tempo_total_meditado', 0)
                self.investimentos_feitos = dados.get('investimentos_feitos', 0)
                self.gratidoes_registradas = dados.get('gratidoes_registradas', 0)
                self.afirmacoes_ativadas = dados.get('afirmacoes_ativadas', 0)

                # Carregar dados dos investimentos
                self.ultimo_investimento = dados.get('ultimo_investimento')
                self.investimentos_ativos = dados.get('investimentos_ativos', {})

                # Carregar dados da loja
                self.moedas_especiais = dados.get('moedas_especiais', 0)
                self.itens_comprados = dados.get('itens_comprados', [])

                # Carregar preferências de zoom
                self.zoom_preferences = dados.get('zoom_preferences', {})
                if self.zoom_preferences:
                    self.zoom_level = self.zoom_preferences.get('zoom_level', 1.0)
                    if self.zoom_preferences.get('alto_contraste', False):
                        self.alto_contraste_ativo = True
                        self.aplicar_alto_contraste()

                return True
        except FileNotFoundError:
            return False

    def carregar_logs(self):
        try:
            with open('logs_jogo.json', 'r') as f:
                logs = json.load(f)
                self.lista_presentes.delete(0, tk.END)
                self.lista_surpresas.delete(0, tk.END)
                self.lista_gratidoes.delete(0, tk.END)
                
                # Carregar presentes
                for presente in logs.get('presentes', []):
                    self.lista_presentes.insert(tk.END, presente)
                
                # Carregar surpresas
                for surpresa in logs.get('surpresas', []):
                    self.lista_surpresas.insert(tk.END, surpresa)
                
                # Carregar gratidões
                for gratidao in logs.get('gratidoes', []):
                    self.lista_gratidoes.insert(tk.END, gratidao)
        except FileNotFoundError:
            pass

    def salvar_logs(self):
        logs = {
            'presentes': self.lista_presentes.get(0, tk.END),
            'surpresas': self.lista_surpresas.get(0, tk.END),
            'gratidoes': self.lista_gratidoes.get(0, tk.END)
        }
        with open('logs_jogo.json', 'w') as f:
            json.dump(logs, f)

    def salvar_gratidao(self):
        texto = self.texto_gratidao.get('1.0', 'end-1c').strip()
        texto_inicial = "Hoje sou feliz e grata pois..."
        
        # Verificar se o usuário escreveu algo além do texto inicial
        if texto == texto_inicial or len(texto) <= len(texto_inicial):
            messagebox.showwarning(
                "✨ Gratidão",
                "Por favor, complete sua gratidão após o texto inicial!"
            )
            return
        
        # Adicionar timestamp
        timestamp = datetime.now().strftime("%d/%m/%Y %H:%M")
        gratidao_completa = f"{timestamp} - {texto}"
        
        # Adicionar à lista
        self.lista_gratidoes.insert(0, gratidao_completa)
        
        # Limpar campo e recolocar texto inicial
        self.texto_gratidao.delete('1.0', 'end')
        self.texto_gratidao.insert('1.0', texto_inicial)
        
        # Recompensar usuário
        self.recompensar_gratidao()

        # Salvar logs
        self.salvar_logs()

    def recompensar_gratidao(self):
        """Recompensa o usuário por registrar uma gratidão"""
        bonus = random.randint(3000, 12000)
        xp_ganho = random.randint(15, 35)

        self.saldo += bonus
        self.xp += xp_ganho
        self.gratidoes_registradas += 1

        # Ganhar moedas especiais ocasionalmente
        bonus_moedas_msg = ""
        if random.random() < 0.4:  # 40% de chance (maior que afirmações)
            moedas_ganhas = random.randint(2, 6)
            self.moedas_especiais += moedas_ganhas
            bonus_moedas_msg = f"\n💎 +{moedas_ganhas} Cristais Universais!"

        # Verificar conquistas e nível
        self.verificar_conquistas()
        self.verificar_nivel()
        self.atualizar_interface()

        # Salvar progresso
        self.salvar_progresso()

        self.mostrar_mensagem(
            "🙏 Gratidão Registrada",
            f"Sua gratidão foi registrada com amor!\n\n"
            f"Recompensas:\n💰 R$ {bonus:,.2f}\n"
            f"⭐ +{xp_ganho} XP{bonus_moedas_msg}"
        )

    def primeiro_acesso(self):
        # Janela de primeiro acesso com dimensões responsivas
        self.primeiro_acesso_window = tk.Toplevel()
        self.primeiro_acesso_window.title("✨ Bem-vindo ao Universo Abundante ✨")
        self.primeiro_acesso_window.configure(bg=self.cores['bg_principal'])
        self.primeiro_acesso_window.resizable(True, True)

        # Calcular dimensões baseadas na resolução
        if self.modo_nano:
            width = min(350, int(self.screen_width * 0.95))
            height = min(250, int(self.screen_height * 0.85))
            font_titulo = ('Segoe UI', 12, 'bold')
            font_texto = ('Segoe UI', 9)
            pady_titulo = 15
        elif self.modo_ultra_compacto:
            width = min(450, int(self.screen_width * 0.9))
            height = min(350, int(self.screen_height * 0.8))
            font_titulo = ('Segoe UI', 14, 'bold')
            font_texto = ('Segoe UI', 10)
            pady_titulo = 20
        else:
            width, height = self.config_responsiva["janela_primeiro_acesso"]
            font_titulo = self.fontes['titulo']
            font_texto = self.fontes['texto']
            pady_titulo = 30

        # Centralizar a janela
        self.centralizar_janela(self.primeiro_acesso_window, width, height)

        # Título responsivo
        tk.Label(
            self.primeiro_acesso_window,
            text="Bem-vindo ao seu\nUniverso Abundante",
            font=font_titulo,
            bg=self.cores['bg_principal'],
            fg=self.cores['dourado'],
            relief='flat',
            bd=0
        ).pack(pady=pady_titulo)
        
        # Frame para entrada de dados
        frame = tk.Frame(
            self.primeiro_acesso_window,
            bg=self.cores['bg_principal']
        )
        frame.pack(pady=20)
        
        # Nome
        tk.Label(
            frame,
            text="Como podemos te chamar?",
            font=self.fontes['texto'],
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        ).pack()
        
        self.nome_entry = tk.Entry(
            frame,
            font=self.fontes['texto'],
            width=30
        )
        self.nome_entry.pack(pady=10)
        
        # Saldo inicial
        tk.Label(
            frame,
            text="Qual valor você deseja manifestar?",
            font=self.fontes['texto'],
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        ).pack()
        
        self.saldo_entry = tk.Entry(
            frame,
            font=self.fontes['texto'],
            width=30
        )
        self.saldo_entry.pack(pady=10)
        
        # Botão de confirmação
        tk.Button(
            frame,
            text="Começar Jornada",
            command=self.confirmar_primeiro_acesso,
            font=self.fontes['botao'],
            bg=self.cores['destaque'],
            fg=self.cores['bg_secundario'],
            relief='flat',
            padx=20,
            pady=10,
            cursor='hand2'
        ).pack(pady=30)

    def confirmar_primeiro_acesso(self):
        nome = self.nome_entry.get().strip()
        saldo_texto = self.saldo_entry.get().strip()
        
        if not nome:
            messagebox.showwarning("Atenção", "Por favor, digite seu nome.")
            return
            
        try:
            saldo = float(saldo_texto.replace("R$", "").replace(".", "").replace(",", "."))
        except:
            messagebox.showwarning("Atenção", "Por favor, digite um valor válido.")
            return
            
        self.nome_usuario = nome
        self.saldo = saldo
        
        # Salvar dados
        self.salvar_progresso()
        
        # Fechar janela de primeiro acesso
        self.primeiro_acesso_window.destroy()
        
        # Mostrar janela principal
        self.setup_interface()

    def salvar_progresso(self):
        dados = {
            'nome_usuario': self.nome_usuario,
            'saldo': self.saldo,
            'nivel': self.nivel,
            'xp': self.xp,
            'xp_proximo_nivel': self.xp_proximo_nivel,
            'ultima_loteria': self.ultima_loteria.isoformat() if self.ultima_loteria else None,
            'modalidade_sorteada': self.modalidade_sorteada,
            'conquistas': self.conquistas,
            'visualizacoes_hoje': self.visualizacoes_hoje,
            'total_meditado': self.total_meditado,
            'tempo_total_meditado': self.tempo_total_meditado,
            'investimentos_feitos': self.investimentos_feitos,
            'gratidoes_registradas': self.gratidoes_registradas,
            'afirmacoes_ativadas': self.afirmacoes_ativadas,
            'ultimo_investimento': self.ultimo_investimento,
            'investimentos_ativos': self.investimentos_ativos,
            'moedas_especiais': self.moedas_especiais,
            'itens_comprados': self.itens_comprados,
            'zoom_preferences': getattr(self, 'zoom_preferences', {})
        }
        with open('dados_jogo.json', 'w') as f:
            json.dump(dados, f)

    def centralizar_janela(self, janela, largura=None, altura=None):
        """Centraliza qualquer janela na tela com dimensões responsivas"""
        janela.update_idletasks()

        # Usar dimensões fornecidas ou obter da janela
        if largura and altura:
            width, height = largura, altura
            janela.geometry(f"{width}x{height}")
        else:
            width = janela.winfo_width()
            height = janela.winfo_height()

        # Garantir que a janela não seja maior que a tela
        max_width = int(self.screen_width * 0.9)
        max_height = int(self.screen_height * 0.9)

        if width > max_width:
            width = max_width
        if height > max_height:
            height = max_height

        # Calcular posição central
        x = (self.screen_width // 2) - (width // 2)
        y = (self.screen_height // 2) - (height // 2)

        # Garantir que a janela não saia da tela
        x = max(0, min(x, self.screen_width - width))
        y = max(0, min(y, self.screen_height - height))

        janela.geometry(f'{width}x{height}+{x}+{y}')

    def configurar_scroll_mouse(self, canvas):
        """Configura scroll do mouse para o canvas"""
        def _on_mousewheel(event):
            # Verificar se o canvas pode fazer scroll
            if canvas.winfo_exists():
                try:
                    # Scroll mais suave
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
                except:
                    pass

        def _on_mousewheel_linux_up(_):
            if canvas.winfo_exists():
                try:
                    canvas.yview_scroll(-1, "units")
                except:
                    pass

        def _on_mousewheel_linux_down(_):
            if canvas.winfo_exists():
                try:
                    canvas.yview_scroll(1, "units")
                except:
                    pass

        # Bind para Windows
        canvas.bind("<MouseWheel>", _on_mousewheel)
        # Bind para Linux
        canvas.bind("<Button-4>", _on_mousewheel_linux_up)
        canvas.bind("<Button-5>", _on_mousewheel_linux_down)

        # Também permitir scroll quando o mouse está sobre o canvas
        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)
            canvas.bind_all("<Button-4>", _on_mousewheel_linux_up)
            canvas.bind_all("<Button-5>", _on_mousewheel_linux_down)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")
            canvas.unbind_all("<Button-4>")
            canvas.unbind_all("<Button-5>")

        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

    def criar_scroll_frame(self, parent):
        """Cria um frame com scroll funcional"""
        # Container principal
        container = tk.Frame(parent, bg=self.cores['bg_principal'])
        container.pack(fill='both', expand=True)

        # Canvas e Scrollbar
        canvas = tk.Canvas(container, bg=self.cores['bg_principal'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.cores['bg_principal'])

        # Configurar scroll region
        def configure_scroll_region(_):
            canvas.configure(scrollregion=canvas.bbox("all"))

        scrollable_frame.bind("<Configure>", configure_scroll_region)

        # Criar janela no canvas
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # Configurar scroll
        canvas.configure(yscrollcommand=scrollbar.set)

        # Função para ajustar largura do frame interno
        def configure_canvas(_):
            # Ajustar largura do frame interno para preencher o canvas
            canvas_width = canvas.winfo_width()
            canvas.itemconfig(canvas_window, width=canvas_width)

        canvas.bind('<Configure>', configure_canvas)

        # Pack do scrollbar e canvas
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Configurar scroll com mouse
        self.configurar_scroll_mouse(canvas)

        return scrollable_frame

    def adicionar_efeito_hover(self, widget, cor_normal, cor_hover, cor_texto_normal=None, cor_texto_hover=None):
        """Adiciona efeito hover a um widget"""
        def on_enter(_):
            widget.config(bg=cor_hover)
            if cor_texto_hover:
                widget.config(fg=cor_texto_hover)

        def on_leave(_):
            widget.config(bg=cor_normal)
            if cor_texto_normal:
                widget.config(fg=cor_texto_normal)

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def criar_botao_moderno(self, parent, texto, comando, tipo="primario", **kwargs):
        """Cria um botão com design moderno e efeitos avançados"""
        # Definir cores baseadas no tipo
        if tipo == "primario":
            bg_normal = self.cores['botao']
            bg_hover = self.cores['botao_hover']
            bg_active = self.cores['botao_active']
            fg_color = self.cores['texto_botao']
        elif tipo == "secundario":
            bg_normal = self.cores['botao_secundario']
            bg_hover = self.cores['botao_secundario_hover']
            bg_active = self.cores['card_active']
            fg_color = self.cores['texto']
        elif tipo == "sucesso":
            bg_normal = self.cores['sucesso']
            bg_hover = self.cores['sucesso_hover']
            bg_active = self.cores['sucesso']
            fg_color = self.cores['texto_botao']
        elif tipo == "erro":
            bg_normal = self.cores['erro']
            bg_hover = self.cores['erro_hover']
            bg_active = self.cores['erro']
            fg_color = self.cores['texto_botao']
        elif tipo == "aviso":
            bg_normal = self.cores['aviso']
            bg_hover = self.cores['aviso_hover']
            bg_active = self.cores['aviso']
            fg_color = self.cores['texto_botao']
        else:
            bg_normal = self.cores['card']
            bg_hover = self.cores['card_hover']
            bg_active = self.cores['card_active']
            fg_color = self.cores['texto']

        # Ajustar padding para modo compacto
        if hasattr(self, 'modo_ultra_compacto') and self.modo_ultra_compacto:
            padx_default = getattr(self, 'padding_botao_compacto', 5)
            pady_default = 3
        else:
            padx_default = 20
            pady_default = 10

        botao = tk.Button(
            parent,
            text=texto,
            command=comando,
            font=kwargs.get('font', self.fontes['botao']),
            bg=bg_normal,
            fg=fg_color,
            relief='flat',
            bd=1,
            highlightthickness=0,
            padx=kwargs.get('padx', padx_default),
            pady=kwargs.get('pady', pady_default),
            cursor='hand2',
            **{k: v for k, v in kwargs.items() if k not in ['font', 'padx', 'pady']}
        )

        # Adicionar efeitos hover avançados
        self.adicionar_efeitos_botao_moderno(botao, bg_normal, bg_hover, bg_active)

        return botao

    def adicionar_efeitos_botao_moderno(self, botao, bg_normal, bg_hover, bg_active):
        """Adiciona efeitos modernos ao botão"""
        def on_enter(_):
            botao.config(bg=bg_hover)
            # Efeito de elevação simulado
            botao.config(relief='raised', bd=1)

        def on_leave(_):
            botao.config(bg=bg_normal)
            botao.config(relief='flat', bd=1)

        def on_click(_):
            botao.config(bg=bg_active)
            # Efeito de clique
            botao.after(100, lambda: botao.config(bg=bg_hover))

        botao.bind("<Enter>", on_enter)
        botao.bind("<Leave>", on_leave)
        botao.bind("<Button-1>", on_click)

    def criar_animacao_fade(self, widget, duracao=300, fade_in=True):
        """Cria animação de fade in/out"""
        if not hasattr(widget, 'winfo_exists') or not widget.winfo_exists():
            return

        steps = 20
        step_duration = duracao // steps

        def animate_step(step):
            if not widget.winfo_exists():
                return

            # Simular transparência mudando estado
            try:
                if fade_in:
                    widget.configure(state='normal' if step == steps else 'disabled')
                else:
                    widget.configure(state='disabled' if step == steps else 'normal')
            except:
                pass

            if step < steps:
                widget.after(step_duration, lambda: animate_step(step + 1))

        animate_step(0)

    def criar_animacao_slide(self, widget, direction='up', duracao=250):
        """Cria animação de slide"""
        if not hasattr(widget, 'winfo_exists') or not widget.winfo_exists():
            return

        steps = 15
        step_duration = duracao // steps

        # Obter posição inicial
        try:
            x_inicial = widget.winfo_x()
            y_inicial = widget.winfo_y()
        except:
            return

        # Definir deslocamento baseado na direção
        if direction == 'up':
            x_offset, y_offset = 0, 20
        elif direction == 'down':
            x_offset, y_offset = 0, -20
        elif direction == 'left':
            x_offset, y_offset = 20, 0
        elif direction == 'right':
            x_offset, y_offset = -20, 0
        else:
            x_offset, y_offset = 0, 20

        def animate_step(step):
            if not widget.winfo_exists():
                return

            progress = step / steps
            current_x = x_inicial + x_offset * (1 - progress)
            current_y = y_inicial + y_offset * (1 - progress)

            try:
                widget.place(x=current_x, y=current_y)
            except:
                pass

            if step < steps:
                widget.after(step_duration, lambda: animate_step(step + 1))
            else:
                # Restaurar posição final
                try:
                    widget.place(x=x_inicial, y=y_inicial)
                except:
                    pass

        animate_step(0)

    def criar_animacao_pulse(self, widget, duracao=1000, cycles=1):
        """Cria animação de pulsação"""
        if not hasattr(widget, 'winfo_exists') or not widget.winfo_exists():
            return

        steps_per_cycle = 30
        total_steps = steps_per_cycle * cycles
        step_duration = duracao // total_steps

        def animate_step(step):
            if not widget.winfo_exists():
                return

            # Aplicar efeito de brilho simples
            try:
                # Simular pulsação mudando relief
                if (step % steps_per_cycle) < (steps_per_cycle // 2):
                    widget.configure(relief='raised')
                else:
                    widget.configure(relief='flat')
            except:
                pass

            if step < total_steps:
                widget.after(step_duration, lambda: animate_step(step + 1))

        animate_step(0)

    def criar_notificacao(self, titulo, mensagem, tipo="info", duracao=5000):
        """Cria uma notificação moderna no canto superior direito"""
        # Evitar muitas notificações simultâneas
        if len(self.notificacoes_ativas) >= 3:
            return

        # Obter dimensões responsivas para notificação
        notif_width, notif_height = self.config_responsiva["notificacao"]

        # Criar janela de notificação
        notif = tk.Toplevel(self)
        notif.title("")
        notif.geometry(f"{notif_width}x{notif_height}")
        notif.resizable(False, False)
        notif.overrideredirect(True)  # Remove barra de título

        # Posicionar no canto superior direito com margem responsiva
        margin = self.obter_padding_responsivo(20)
        x = self.screen_width - notif_width - margin
        y = margin + (len(self.notificacoes_ativas) * (notif_height + 10))
        notif.geometry(f"{notif_width}x{notif_height}+{x}+{y}")

        # Configurar cores modernas baseadas no tipo
        if tipo == "sucesso":
            bg_color = self.cores['sucesso_bg']
            border_color = self.cores['sucesso']
            icon_color = self.cores['sucesso']
            text_color = self.cores['texto']
            icon = "✅"
        elif tipo == "erro":
            bg_color = self.cores['erro_bg']
            border_color = self.cores['erro']
            icon_color = self.cores['erro']
            text_color = self.cores['texto']
            icon = "❌"
        elif tipo == "aviso":
            bg_color = self.cores['aviso_bg']
            border_color = self.cores['aviso']
            icon_color = self.cores['aviso']
            text_color = self.cores['texto']
            icon = "⚠️"
        else:
            bg_color = self.cores['info_bg']
            border_color = self.cores['info']
            icon_color = self.cores['info']
            text_color = self.cores['texto']
            icon = "ℹ️"

        # Container principal com borda colorida
        main_container = tk.Frame(notif, bg=border_color)
        main_container.pack(fill='both', expand=True)

        # Container interno com fundo escuro
        inner_container = tk.Frame(main_container, bg=bg_color)
        inner_container.pack(fill='both', expand=True, padx=2, pady=2)

        # Header com ícone e botão fechar
        header_frame = tk.Frame(inner_container, bg=bg_color)
        header_frame.pack(fill='x', padx=8, pady=(8, 4))

        # Ícone
        icon_label = tk.Label(
            header_frame,
            text=icon,
            font=('Segoe UI Emoji', 14),
            bg=bg_color,
            fg=icon_color
        )
        icon_label.pack(side='left')

        # Título
        titulo_label = tk.Label(
            header_frame,
            text=titulo,
            font=(self.fontes['texto'][0], self.fontes['texto'][1], 'bold'),
            bg=bg_color,
            fg=text_color
        )
        titulo_label.pack(side='left', padx=(8, 0))

        # Botão fechar
        close_btn = tk.Label(
            header_frame,
            text="×",
            font=('Segoe UI', 16, 'bold'),
            bg=bg_color,
            fg=self.cores['texto_muted'],
            cursor='hand2'
        )
        close_btn.pack(side='right')

        # Mensagem
        msg_label = tk.Label(
            inner_container,
            text=mensagem,
            font=self.fontes['texto_pequeno'],
            bg=bg_color,
            fg=self.cores['texto_secundario'],
            wraplength=notif_width - 20,
            justify='left'
        )
        msg_label.pack(anchor='w', padx=(32, 8), pady=(0, 8))

        # Adicionar à lista de notificações ativas
        self.notificacoes_ativas.append(notif)

        # Programar fechamento automático
        def fechar_notificacao():
            if notif in self.notificacoes_ativas:
                self.notificacoes_ativas.remove(notif)
            try:
                # Animação de fade out
                self.criar_animacao_fade(notif, duracao=200, fade_in=False)
                notif.after(200, notif.destroy)
            except:
                try:
                    notif.destroy()
                except:
                    pass

        notif.after(duracao, fechar_notificacao)

        # Permitir fechar clicando no X ou na notificação
        def fechar_click(_):
            fechar_notificacao()

        close_btn.bind("<Button-1>", fechar_click)
        notif.bind("<Button-1>", fechar_click)

        # Efeito hover no botão fechar
        def on_close_hover(_):
            close_btn.config(fg=self.cores['texto'])
        def on_close_leave(_):
            close_btn.config(fg=self.cores['texto_muted'])

        close_btn.bind("<Enter>", on_close_hover)
        close_btn.bind("<Leave>", on_close_leave)

        # Animação de entrada
        self.criar_animacao_slide(notif, direction='left', duracao=300)

        return notif

    def setup_interface(self):
        # Configuração da janela principal com dimensões responsivas
        self.title("✨ Universo Abundante ✨")
        self.configure(bg=self.cores['bg_principal'])
        self.resizable(True, True)

        # Definir tamanho mínimo baseado na resolução
        min_width = int(self.config_responsiva["janela_principal"][0] * 0.8)
        min_height = int(self.config_responsiva["janela_principal"][1] * 0.8)
        self.minsize(min_width, min_height)

        # Centralizar a janela principal
        width, height = self.config_responsiva["janela_principal"]
        self.centralizar_janela(self, width, height)
        
        # Container principal com padding responsivo
        padding = self.obter_padding_responsivo(20)
        self.main_container = tk.Frame(
            self,
            bg=self.cores['bg_principal'],
            padx=padding,
            pady=padding
        )
        self.main_container.pack(fill='both', expand=True)
        
        # Header
        self.criar_header()
        
        # Card principal
        self.criar_card_principal()
        
        # Notebook (sistema de abas)
        self.notebook = ttk.Notebook(self.main_container)
        self.notebook.pack(fill='both', expand=True, pady=10)
        
        # Criar todas as abas
        self.criar_aba_principal()      # Aba Principal
        self.criar_aba_visualizacao()   # Aba Visualização
        self.criar_aba_meditacao()      # Aba Meditação (NOVA)
        self.criar_aba_gratidao()       # Aba Gratidão
        self.criar_aba_investimentos()  # Aba Investimentos
        self.criar_aba_surpresas()      # Aba Surpresas
        self.criar_aba_presentes()      # Aba Presentes
        self.criar_aba_conquistas()     # Aba Conquistas
        self.criar_aba_loja()           # Aba Loja (NOVA)
        self.criar_aba_dashboard()      # Aba Dashboard (NOVA)

        # Configurar redimensionamento automático
        self.configurar_redimensionamento_automatico()

        # Aplicar modo ultra compacto se necessário
        self.aplicar_modo_ultra_compacto()

        # Otimizações específicas para 1366x768
        self.otimizar_para_1366x768()

        # Criar menu de zoom para ajustes manuais
        self.criar_menu_zoom()

    def criar_header(self):
        # Header principal com dimensões responsivas e modo ultra compacto
        if self.modo_ultra_compacto:
            header_height = self.obter_padding_responsivo(80)  # Muito menor para telas pequenas
        else:
            header_height = self.obter_padding_responsivo(120)

        header = tk.Frame(
            self.main_container,
            bg=self.cores['bg_principal'],
            height=header_height
        )
        header.pack(fill='x', pady=(0, self.obter_padding_responsivo(10 if self.modo_ultra_compacto else 20)))
        header.pack_propagate(False)

        # Container interno com padding responsivo
        padding_interno = 5 if self.modo_ultra_compacto else 10
        header_content = tk.Frame(
            header,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        header_content.pack(fill='both', expand=True, padx=padding_interno, pady=padding_interno)

        if self.modo_ultra_compacto:
            # Layout simplificado para telas muito pequenas
            self.criar_header_compacto(header_content)
        else:
            # Layout normal para telas maiores
            self.criar_header_normal(header_content)

    def criar_header_compacto(self, parent):
        """Cria header simplificado para telas muito pequenas com efeito premium"""
        # Ajustar para modo nano
        if self.modo_nano:
            pady_container = 2
            padx_container = 2
            bd_container = 2
            pady_inner = 2
            padx_inner = 2
            bd_inner = 1
        else:
            pady_container = 4
            padx_container = 3
            bd_container = 3
            pady_inner = 3
            padx_inner = 3
            bd_inner = 2

        # Container premium com melhor contraste
        premium_container = tk.Frame(
            parent,
            bg=self.cores['card'],
            relief='raised',
            bd=bd_container
        )
        premium_container.pack(fill='x', pady=pady_container, padx=padx_container)

        # Frame interno com fundo escuro para melhor contraste
        inner_frame = tk.Frame(
            premium_container,
            bg=self.cores['bg_secundario'],
            relief='flat',
            bd=0
        )
        inner_frame.pack(fill='both', expand=True, padx=padx_inner, pady=pady_inner)

        # Título MANIFESTADOR PREMIUM com tamanho adequado
        titulo_frame = tk.Frame(inner_frame, bg=self.cores['bg_secundario'])
        if self.modo_nano:
            titulo_frame.pack(fill='x', pady=(2, 1))
            titulo_text = "⭐ PREMIUM ⭐"
            font_size = 10  # Tamanho fixo pequeno para nano
        elif self.modo_ultra_compacto:
            titulo_frame.pack(fill='x', pady=(4, 2))
            titulo_text = "✨ MANIFESTADOR PREMIUM ✨"
            font_size = 12  # Tamanho fixo médio para compacto
        else:
            titulo_frame.pack(fill='x', pady=(6, 4))
            titulo_text = "✨ MANIFESTADOR PREMIUM ✨"
            font_size = 14  # Tamanho fixo normal

        # Título principal com alta legibilidade
        titulo_main = tk.Label(
            titulo_frame,
            text=titulo_text,
            font=('Segoe UI', font_size, 'bold'),
            bg=self.cores['bg_secundario'],
            fg=self.cores['dourado'],
            relief='flat',
            bd=0
        )
        titulo_main.pack(pady=2)

        # Separador dourado com gradiente
        separador_container = tk.Frame(inner_frame, bg=self.cores['premium_bg'])
        separador_container.pack(fill='x', padx=15, pady=3)

        separador = tk.Frame(
            separador_container,
            bg=self.cores['dourado'],
            height=3
        )
        separador.pack(fill='x')

        # Informações do usuário com melhor legibilidade
        info_frame = tk.Frame(inner_frame, bg=self.cores['bg_secundario'])
        info_frame.pack(fill='x', pady=(6, 8), padx=12)

        # Nome do usuário com alta legibilidade
        nome_container = tk.Frame(info_frame, bg=self.cores['bg_secundario'])
        nome_container.pack(side='left')

        # Nome principal com tamanho adequado
        if self.modo_nano:
            font_size_nome = 9
        elif self.modo_ultra_compacto:
            font_size_nome = 10
        else:
            font_size_nome = 12

        # Verificar se o nome do usuário existe
        nome_exibir = self.nome_usuario.upper() if self.nome_usuario else "USUÁRIO"

        nome_main = tk.Label(
            nome_container,
            text=f"👑 {nome_exibir}",
            font=('Segoe UI', font_size_nome, 'bold'),
            bg=self.cores['bg_secundario'],
            fg=self.cores['texto'],
            relief='flat',
            bd=0
        )
        nome_main.pack(pady=1)

        # Nível com alta legibilidade
        nivel_container = tk.Frame(info_frame, bg=self.cores['bg_secundario'])
        nivel_container.pack(side='right')

        # Nível principal com tamanho adequado
        if self.modo_nano:
            font_size_nivel = 8
        elif self.modo_ultra_compacto:
            font_size_nivel = 9
        else:
            font_size_nivel = 10

        nivel_main = tk.Label(
            nivel_container,
            text=f"🏆 NÍVEL {self.nivel}",
            font=('Segoe UI', font_size_nivel, 'bold'),
            bg=self.cores['bg_secundario'],
            fg=self.cores['dourado'],
            relief='flat',
            bd=0
        )
        nivel_main.pack(pady=1)

    def criar_header_normal(self, parent):
        """Cria header normal para telas maiores"""
        # Frame esquerdo - Saudação e informações
        left_frame = tk.Frame(
            parent,
            bg=self.cores['card']
        )
        left_frame.pack(side='left', fill='y', padx=self.obter_padding_responsivo(20), pady=self.obter_padding_responsivo(10))

        # Container premium com efeito especial
        premium_container = tk.Frame(
            left_frame,
            bg=self.cores['destaque'],
            relief='raised',
            bd=3
        )
        premium_container.pack(anchor='w', pady=(0, 8), padx=2)

        # Frame interno para o título
        titulo_inner = tk.Frame(
            premium_container,
            bg=self.cores['bg_secundario'],
            relief='sunken',
            bd=2
        )
        titulo_inner.pack(fill='both', expand=True, padx=3, pady=3)

        # Título MANIFESTADOR PREMIUM com efeito de brilho
        titulo_frame = tk.Frame(titulo_inner, bg=self.cores['bg_secundario'])
        titulo_frame.pack(pady=8, padx=15)

        # Sombra do título
        titulo_shadow = tk.Label(
            titulo_frame,
            text="✨ MANIFESTADOR PREMIUM ✨",
            font=(self.fontes['titulo'][0], self.fontes['titulo'][1], 'bold'),
            bg=self.cores['bg_secundario'],
            fg=self.cores['borda']
        )
        titulo_shadow.pack()

        # Título principal dourado
        titulo_main = tk.Label(
            titulo_frame,
            text="✨ MANIFESTADOR PREMIUM ✨",
            font=(self.fontes['titulo'][0], self.fontes['titulo'][1], 'bold'),
            bg=self.cores['bg_secundario'],
            fg=self.cores['dourado']
        )
        titulo_main.place(in_=titulo_shadow, x=-2, y=-2)

        # Borda decorativa dourada
        borda_decorativa = tk.Frame(
            titulo_inner,
            bg=self.cores['dourado'],
            height=3
        )
        borda_decorativa.pack(fill='x', padx=10)

        # Saudação personalizada
        hora = datetime.now().hour
        if 5 <= hora < 12:
            saudacao = "🌅 Bom dia"
        elif 12 <= hora < 18:
            saudacao = "🌞 Boa tarde"
        else:
            saudacao = "🌙 Boa noite"

        saudacao_frame = tk.Frame(left_frame, bg=self.cores['card'])
        saudacao_frame.pack(anchor='w', pady=2)

        tk.Label(
            saudacao_frame,
            text=saudacao,
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack(side='left')

        # Nome do usuário com efeito premium
        nome_container = tk.Frame(
            left_frame,
            bg=self.cores['card'],
            relief='ridge',
            bd=2
        )
        nome_container.pack(anchor='w', pady=(8, 0), padx=2)

        nome_inner = tk.Frame(
            nome_container,
            bg=self.cores['bg_secundario']
        )
        nome_inner.pack(fill='both', expand=True, padx=3, pady=3)

        nome_frame = tk.Frame(nome_inner, bg=self.cores['bg_secundario'])
        nome_frame.pack(pady=5, padx=10)

        # Verificar se o nome do usuário existe
        nome_exibir = self.nome_usuario.upper() if self.nome_usuario else "USUÁRIO"

        # Nome principal com tamanho adequado
        nome_main = tk.Label(
            nome_frame,
            text=f"👑 {nome_exibir}",
            font=('Segoe UI', 14, 'bold'),
            bg=self.cores['bg_secundario'],
            fg=self.cores['texto'],
            relief='flat',
            bd=0
        )
        nome_main.pack(pady=2)

        # Frame direito - Estatísticas rápidas
        right_frame = tk.Frame(
            parent,
            bg=self.cores['card']
        )
        right_frame.pack(side='right', fill='y', padx=self.obter_padding_responsivo(20), pady=self.obter_padding_responsivo(10))

        # Mini estatísticas
        stats_frame = tk.Frame(right_frame, bg=self.cores['card'])
        stats_frame.pack(anchor='e')

        # Nível atual
        nivel_mini = tk.Frame(stats_frame, bg=self.cores['card'])
        nivel_mini.pack(anchor='e', pady=2)

        tk.Label(
            nivel_mini,
            text="🏆",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['dourado']
        ).pack(side='left')

        tk.Label(
            nivel_mini,
            text=f" Nível {self.nivel}",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left')

        # Data atual
        data_frame = tk.Frame(stats_frame, bg=self.cores['card'])
        data_frame.pack(anchor='e', pady=2)

        tk.Label(
            data_frame,
            text="📅",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack(side='left')

        tk.Label(
            data_frame,
            text=f" {datetime.now().strftime('%d/%m/%Y')}",
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack(side='left')

    def criar_card_principal(self):
        # Card principal com design moderno e responsivo
        card_container = tk.Frame(
            self.main_container,
            bg=self.cores['bg_principal']
        )
        # Padding ainda menor para telas muito pequenas
        if self.categoria_resolucao in ["nano", "micro", "mini"]:
            pady_card = 3
        else:
            pady_card = 8 if self.modo_ultra_compacto else 15
        card_container.pack(fill='x', pady=pady_card)

        # Sombra moderna com múltiplas camadas
        shadow_outer = tk.Frame(
            card_container,
            bg=self.cores['sombra_forte'],
            height=2
        )
        shadow_outer.pack(fill='x', padx=14, pady=(4, 0))

        shadow_inner = tk.Frame(
            card_container,
            bg=self.cores['sombra'],
            height=1
        )
        shadow_inner.pack(fill='x', padx=13, pady=(1, 0))

        # Padding responsivo para o card
        if self.modo_nano:
            padx_card = 8
            pady_card = 6
            padx_container = 3
        elif self.modo_ultra_compacto:
            padx_card = 15
            pady_card = 12
            padx_container = 5
        else:
            padx_card = 30
            pady_card = 25
            padx_container = 10

        # Card principal com bordas arredondadas simuladas
        card_border = tk.Frame(
            card_container,
            bg=self.cores['borda'],
            relief='flat',
            bd=0
        )
        card_border.pack(fill='x', padx=padx_container)

        card = tk.Frame(
            card_border,
            bg=self.cores['card'],
            padx=padx_card,
            pady=pady_card,
            relief='flat',
            bd=0
        )
        card.pack(fill='x', padx=1, pady=1)

        # Header do card com ícone
        header_card = tk.Frame(card, bg=self.cores['card'])
        header_card.pack(fill='x', pady=(0, 15))

        # Ícone e título do saldo
        titulo_saldo_frame = tk.Frame(header_card, bg=self.cores['card'])
        titulo_saldo_frame.pack(side='left')

        tk.Label(
            titulo_saldo_frame,
            text="💰",
            font=self.fontes['emoji_grande'],
            bg=self.cores['card'],
            fg=self.cores['dourado']
        ).pack(side='left')

        tk.Label(
            titulo_saldo_frame,
            text=" Patrimônio Manifestado",
            font=self.fontes['subtitulo'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack(side='left')

        # Status de crescimento
        status_frame = tk.Frame(header_card, bg=self.cores['card'])
        status_frame.pack(side='right')

        tk.Label(
            status_frame,
            text="📈 Em Crescimento",
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['sucesso']
        ).pack()

        # Valor do saldo com formatação melhorada
        saldo_frame = tk.Frame(card, bg=self.cores['card'])
        saldo_frame.pack(fill='x', pady=(0, 20))

        self.saldo_label = tk.Label(
            saldo_frame,
            text=f"R$ {self.saldo:,.2f}",
            font=self.fontes['numeros_grandes'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        )
        self.saldo_label.pack(side='left')

        # Separador visual
        separador = tk.Frame(card, bg=self.cores['borda'], height=1)
        separador.pack(fill='x', pady=10)

        # Seção de progresso melhorada
        progress_section = tk.Frame(card, bg=self.cores['card'])
        progress_section.pack(fill='x')

        # Header do progresso
        progress_header = tk.Frame(progress_section, bg=self.cores['card'])
        progress_header.pack(fill='x', pady=(0, 10))

        # Nível com ícone
        nivel_frame = tk.Frame(progress_header, bg=self.cores['card'])
        nivel_frame.pack(side='left')

        tk.Label(
            nivel_frame,
            text="⭐",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['dourado']
        ).pack(side='left')

        self.nivel_label = tk.Label(
            nivel_frame,
            text=f" Nível {self.nivel}",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        )
        self.nivel_label.pack(side='left')

        # XP com formatação melhorada
        self.xp_label = tk.Label(
            progress_header,
            text=f"XP: {self.xp:,}/{self.xp_proximo_nivel:,}",
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        )
        self.xp_label.pack(side='right')

        # Container da barra de progresso
        progress_container = tk.Frame(progress_section, bg=self.cores['card'])
        progress_container.pack(fill='x')

        # Barra de progresso customizada
        progress_bg = tk.Frame(
            progress_container,
            bg=self.cores['progresso_bg'],
            height=8
        )
        progress_bg.pack(fill='x')

        # Configurar estilo da barra de progresso
        style = ttk.Style()
        style.configure(
            "Modern.Horizontal.TProgressbar",
            troughcolor=self.cores['progresso_bg'],
            background=self.cores['progresso'],
            thickness=8,
            relief='flat',
            borderwidth=0
        )

        self.progress = ttk.Progressbar(
            progress_bg,
            style="Modern.Horizontal.TProgressbar",
            mode='determinate'
        )
        self.progress.pack(fill='x')
        self.progress['value'] = (self.xp / self.xp_proximo_nivel) * 100

        # Porcentagem do progresso
        porcentagem = (self.xp / self.xp_proximo_nivel) * 100
        progress_percent = tk.Label(
            progress_container,
            text=f"{porcentagem:.1f}% para o próximo nível",
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        )
        progress_percent.pack(pady=(5, 0))

    def atualizar_interface(self):
        # Atualizar saldo
        self.saldo_label.config(text=f"R$ {self.saldo:,.2f}")
        
        # Atualizar informações de nível
        self.nivel_label.config(text=f"Nível {self.nivel}")
        self.xp_label.config(text=f"XP: {self.xp}/{self.xp_proximo_nivel}")
        
        # Atualizar barra de progresso
        porcentagem = (self.xp / self.xp_proximo_nivel) * 100
        self.progress['value'] = porcentagem
        
        # Forçar atualização visual
        self.update_idletasks()
        
        # Salvar progresso
        self.salvar_progresso()

    def criar_aba_principal(self):
        # Criar frame da aba
        aba = tk.Frame(self.notebook, bg=self.cores['bg_principal'])
        self.notebook.add(aba, text="🏠 Principal")

        # Usar a função centralizada de scroll
        scrollable_frame = self.criar_scroll_frame(aba)
        
        # Frame com padding para o conteúdo
        frame = tk.Frame(scrollable_frame, bg=self.cores['bg_principal'], padx=20, pady=20)
        frame.pack(fill='both', expand=True)
        
        # Frame para centralizar o conteúdo
        frame_central = tk.Frame(frame, bg=self.cores['bg_principal'])
        frame_central.pack(expand=True)
        
        # Título
        tk.Label(
            frame_central,
            text="Afirmações Poderosas",
            font=('Helvetica', 16, 'bold'),
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        ).pack(pady=(0, 20))
        
        # Frame para as afirmações
        frame_afirmacoes = tk.Frame(frame_central, bg=self.cores['bg_principal'])
        frame_afirmacoes.pack(expand=True)
        
        # Afirmações
        afirmacoes = [
            "✨ Eu sou um ímã de abundância e prosperidade",
            "💫 O dinheiro flui para mim facilmente",
            "🌟 Eu mereço e atraio riqueza infinita",
            "💎 Sou grato por toda abundância em minha vida",
            "🌈 O Universo conspira a meu favor",
            "⭐ Todas as minhas ações resultam em prosperidade",
            "🌺 Eu sou próspero em todas as áreas da minha vida",
            "💫 Minha conta bancária cresce todos os dias"
        ]
        
        # Criar botões modernos para afirmações
        for i, afirmacao in enumerate(afirmacoes):
            # Container para cada afirmação
            afirmacao_container = tk.Frame(frame_afirmacoes, bg=self.cores['bg_principal'])
            afirmacao_container.pack(fill='x', pady=8, padx=20)

            # Card da afirmação
            afirmacao_card = tk.Frame(
                afirmacao_container,
                bg=self.cores['card'],
                relief='flat',
                bd=0
            )
            afirmacao_card.pack(fill='x', padx=5, pady=2)

            # Botão da afirmação com design moderno
            btn = self.criar_botao_moderno(
                afirmacao_card,
                afirmacao,
                lambda a=afirmacao: self.ativar_afirmacao(a),
                tipo="primario",
                font=self.fontes['texto'],
                wraplength=450
            )
            btn.pack(fill='x', padx=15, pady=12)

            # Adicionar efeito de pulsação para afirmações especiais
            if i % 3 == 0:  # A cada 3 afirmações, adicionar destaque
                btn.config(bg=self.cores['destaque'])
                self.adicionar_efeito_hover(btn, self.cores['destaque'], self.cores['destaque_hover'])

    def criar_aba_visualizacao(self):
        aba_visualizacao = tk.Frame(
            self.notebook,
            bg=self.cores['bg_principal']
        )
        self.notebook.add(aba_visualizacao, text="👁️ Visualização")

        # Container principal
        container = tk.Frame(aba_visualizacao, bg=self.cores['bg_principal'])
        container.pack(fill='both', expand=True, padx=20, pady=20)

        # Card principal de visualização
        main_card = tk.Frame(
            container,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        main_card.pack(fill='x', pady=(0, 20))

        # Header do card
        header_frame = tk.Frame(main_card, bg=self.cores['card'])
        header_frame.pack(fill='x', padx=30, pady=(25, 15))

        # Título com ícone
        titulo_frame = tk.Frame(header_frame, bg=self.cores['card'])
        titulo_frame.pack()

        tk.Label(
            titulo_frame,
            text="🧘‍♀️",
            font=self.fontes['emoji_grande'],
            bg=self.cores['card'],
            fg=self.cores['destaque']
        ).pack(side='left')

        tk.Label(
            titulo_frame,
            text=" Visualização Criativa",
            font=self.fontes['titulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left')

        # Descrição
        desc_label = tk.Label(
            header_frame,
            text="Conecte-se com seus sonhos e manifeste sua realidade",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        )
        desc_label.pack(pady=(5, 0))

        # Seção do timer
        timer_section = tk.Frame(main_card, bg=self.cores['card'])
        timer_section.pack(fill='x', padx=30, pady=15)

        # Timer com design circular simulado
        timer_container = tk.Frame(timer_section, bg=self.cores['card'])
        timer_container.pack()

        # Timer display
        self.tempo_visualizacao = 17
        timer_bg = tk.Frame(
            timer_container,
            bg=self.cores['progresso_bg'],
            width=120,
            height=120
        )
        timer_bg.pack(padx=20, pady=10)
        timer_bg.pack_propagate(False)

        self.timer_label = tk.Label(
            timer_bg,
            text=f"{self.tempo_visualizacao}s",
            font=self.fontes['numeros'],
            bg=self.cores['progresso_bg'],
            fg=self.cores['texto']
        )
        self.timer_label.pack(expand=True)

        # Botão de iniciar visualização
        botao_container = tk.Frame(main_card, bg=self.cores['card'])
        botao_container.pack(fill='x', padx=30, pady=15)

        self.btn_visualizar = self.criar_botao_moderno(
            botao_container,
            "🌟 Iniciar Visualização",
            self.iniciar_visualizacao,
            tipo="primario",
            font=self.fontes['botao']
        )
        self.btn_visualizar.pack(pady=10)

        # Estatísticas
        stats_frame = tk.Frame(main_card, bg=self.cores['card'])
        stats_frame.pack(fill='x', padx=30, pady=(0, 25))

        # Separador
        separador = tk.Frame(stats_frame, bg=self.cores['borda'], height=1)
        separador.pack(fill='x', pady=(10, 15))

        # Contador de visualizações
        stats_container = tk.Frame(stats_frame, bg=self.cores['card'])
        stats_container.pack()

        tk.Label(
            stats_container,
            text="📊",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['sucesso']
        ).pack(side='left')

        self.label_visualizacoes = tk.Label(
            stats_container,
            text=f" Visualizações hoje: {self.visualizacoes_hoje}",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        )
        self.label_visualizacoes.pack(side='left')

        # Card de dicas
        dicas_card = tk.Frame(
            container,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        dicas_card.pack(fill='x')

        # Header das dicas
        dicas_header = tk.Frame(dicas_card, bg=self.cores['card'])
        dicas_header.pack(fill='x', padx=30, pady=(20, 10))

        tk.Label(
            dicas_header,
            text="💡 Dicas para uma Visualização Poderosa",
            font=self.fontes['subtitulo'],
            bg=self.cores['card'],
            fg=self.cores['destaque']
        ).pack()

        # Lista de dicas
        dicas = [
            "🎯 Seja específico nos detalhes",
            "❤️ Sinta as emoções como se já fosse real",
            "🌈 Use todos os seus sentidos",
            "⭐ Visualize com gratidão e alegria"
        ]

        dicas_container = tk.Frame(dicas_card, bg=self.cores['card'])
        dicas_container.pack(fill='x', padx=30, pady=(0, 20))

        for dica in dicas:
            dica_label = tk.Label(
                dicas_container,
                text=dica,
                font=self.fontes['texto_pequeno'],
                bg=self.cores['card'],
                fg=self.cores['texto_secundario'],
                anchor='w'
            )
            dica_label.pack(fill='x', pady=2)

    def iniciar_visualizacao(self):
        self.btn_visualizar.config(state='disabled')
        self.atualizar_timer()

    def atualizar_timer(self):
        if self.tempo_visualizacao > 0:
            self.timer_label.config(text=f"Tempo: {self.tempo_visualizacao} segundos")
            self.tempo_visualizacao -= 1
            self.after(1000, self.atualizar_timer)
        else:
            self.finalizar_visualizacao()

    def finalizar_visualizacao(self):
        self.tempo_visualizacao = 17
        self.visualizacoes_hoje += 1
        self.label_visualizacoes.config(text=f" Visualizações hoje: {self.visualizacoes_hoje}")
        self.btn_visualizar.config(state='normal')
        self.timer_label.config(text=f"{self.tempo_visualizacao}s")

        # Tocar som suave de brilho (frequência: 1000Hz, duração: 500ms)
        try:
            winsound.Beep(1000, 500)
        except:
            pass  # Ignorar erro de som se não disponível

        # Recompensa
        bonus = random.randint(10000, 50000)
        self.saldo += bonus
        self.xp += 50

        # Ganhar moedas especiais ocasionalmente
        bonus_moedas_msg = ""
        if random.random() < 0.25:  # 25% de chance
            moedas_ganhas = random.randint(1, 4)
            self.moedas_especiais += moedas_ganhas
            bonus_moedas_msg = f"\n💎 +{moedas_ganhas} Cristais Universais!"

        # Verificar conquistas
        self.verificar_conquistas()

        self.atualizar_interface()
        self.salvar_progresso()

        self.mostrar_mensagem(
            "✨ Visualização Concluída",
            f"Parabéns! Você completou sua visualização!\n\n"
            f"Recompensas:\n💰 R$ {bonus:,.2f}\n"
            f"⭐ 50 XP{bonus_moedas_msg}"
        )

    def criar_aba_meditacao(self):
        """Nova aba de meditação interativa"""
        aba_meditacao = tk.Frame(
            self.notebook,
            bg=self.cores['bg_principal']
        )
        self.notebook.add(aba_meditacao, text="🧘‍♀️ Meditação")

        # Container principal com padding responsivo
        padding = self.obter_padding_responsivo(20)
        container = tk.Frame(aba_meditacao, bg=self.cores['bg_principal'])
        container.pack(fill='both', expand=True, padx=padding, pady=padding)

        # Card principal
        main_card = tk.Frame(
            container,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        main_card.pack(fill='x', pady=(0, 20))

        # Header
        header_frame = tk.Frame(main_card, bg=self.cores['card'])
        header_frame.pack(fill='x', padx=30, pady=(25, 15))

        titulo_frame = tk.Frame(header_frame, bg=self.cores['card'])
        titulo_frame.pack()

        tk.Label(
            titulo_frame,
            text="🧘‍♀️",
            font=self.fontes['emoji_grande'],
            bg=self.cores['card'],
            fg=self.cores['sucesso']
        ).pack(side='left')

        tk.Label(
            titulo_frame,
            text=" Centro de Meditação",
            font=self.fontes['titulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left')

        # Tipos de meditação
        tipos_frame = tk.Frame(main_card, bg=self.cores['card'])
        tipos_frame.pack(fill='x', padx=30, pady=15)

        tk.Label(
            tipos_frame,
            text="Escolha seu tipo de meditação:",
            font=self.fontes['subtitulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(pady=(0, 15))

        # Grid responsivo de tipos de meditação
        tipos_meditacao = [
            {"nome": "🌅 Mindfulness", "tempo": 5, "desc": "Atenção plena", "bonus": (5000, 15000)},
            {"nome": "💰 Abundância", "tempo": 10, "desc": "Foco na prosperidade", "bonus": (10000, 25000)},
            {"nome": "❤️ Amor Próprio", "tempo": 7, "desc": "Autocompaixão", "bonus": (7000, 18000)},
            {"nome": "🌟 Manifestação", "tempo": 15, "desc": "Criação da realidade", "bonus": (15000, 35000)},
            {"nome": "🧠 Clareza Mental", "tempo": 8, "desc": "Foco e concentração", "bonus": (8000, 20000)},
            {"nome": "🌈 Gratidão", "tempo": 6, "desc": "Apreciação profunda", "bonus": (6000, 16000)}
        ]

        # Usar grid responsivo
        def criar_card_meditacao(parent, tipo, row, col):
            colunas = self.obter_colunas_grid_responsivas()
            padding = self.obter_padding_responsivo(10)

            tipo_container = tk.Frame(parent, bg=self.cores['card'])
            tipo_container.grid(row=row, column=col, padx=padding, pady=8, sticky='ew')

            # Ajustar largura do card baseado no número de colunas
            card_width = int(200 * (3 / colunas))  # Ajustar largura baseado nas colunas

            # Card do tipo com dimensões responsivas
            card_height = self.obter_padding_responsivo(100)
            tipo_card = tk.Frame(
                tipo_container,
                bg=self.cores['bg_secundario'],
                relief='flat',
                bd=0,
                width=card_width,
                height=card_height
            )
            tipo_card.pack(fill='both', expand=True, padx=5, pady=5)
            tipo_card.pack_propagate(False)

            # Nome do tipo
            tk.Label(
                tipo_card,
                text=tipo['nome'],
                font=self.fontes['texto'],
                bg=self.cores['bg_secundario'],
                fg=self.cores['texto']
            ).pack(pady=(10, 5))

            # Descrição
            tk.Label(
                tipo_card,
                text=tipo['desc'],
                font=self.fontes['texto_pequeno'],
                bg=self.cores['bg_secundario'],
                fg=self.cores['texto_secundario']
            ).pack()

            # Tempo
            tk.Label(
                tipo_card,
                text=f"{tipo['tempo']} min",
                font=self.fontes['texto_pequeno'],
                bg=self.cores['bg_secundario'],
                fg=self.cores['destaque']
            ).pack()

            # Botão
            btn = self.criar_botao_moderno(
                tipo_card,
                "Meditar",
                lambda t=tipo: self.iniciar_meditacao(t),
                tipo="sucesso",
                font=self.fontes['texto_pequeno']
            )
            btn.pack(pady=(5, 10), padx=10)

        # Criar grid responsivo
        self.criar_grid_responsivo(tipos_frame, tipos_meditacao, criar_card_meditacao)

        # Estatísticas de meditação
        stats_card = tk.Frame(
            container,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        stats_card.pack(fill='x')

        stats_header = tk.Frame(stats_card, bg=self.cores['card'])
        stats_header.pack(fill='x', padx=30, pady=(20, 10))

        tk.Label(
            stats_header,
            text="📊 Suas Estatísticas de Meditação",
            font=self.fontes['subtitulo'],
            bg=self.cores['card'],
            fg=self.cores['destaque']
        ).pack()

        stats_content = tk.Frame(stats_card, bg=self.cores['card'])
        stats_content.pack(fill='x', padx=30, pady=(0, 20))

        # Estatísticas em grid
        stats_grid = tk.Frame(stats_content, bg=self.cores['card'])
        stats_grid.pack()

        # Total de sessões
        sessoes_frame = tk.Frame(stats_grid, bg=self.cores['card'])
        sessoes_frame.grid(row=0, column=0, padx=20, pady=5)

        tk.Label(
            sessoes_frame,
            text="🧘‍♀️",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['sucesso']
        ).pack()

        self.label_total_meditado = tk.Label(
            sessoes_frame,
            text=f"{self.total_meditado} sessões",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        )
        self.label_total_meditado.pack()

        # Tempo total
        tempo_frame = tk.Frame(stats_grid, bg=self.cores['card'])
        tempo_frame.grid(row=0, column=1, padx=20, pady=5)

        tk.Label(
            tempo_frame,
            text="⏰",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['destaque']
        ).pack()

        self.label_tempo_total = tk.Label(
            tempo_frame,
            text=f"{self.tempo_total_meditado} min",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        )
        self.label_tempo_total.pack()

        # Variáveis de controle da meditação
        self.meditacao_ativa = False
        self.tempo_meditacao_atual = 0
        self.tipo_meditacao_atual = None

        # Sistema de notificações
        self.notificacoes_ativas = []
        self.ultima_notificacao = None

    def detectar_resolucao(self):
        """Detecta a resolução da tela e define configurações responsivas"""
        # Obter dimensões da tela
        self.screen_width = self.winfo_screenwidth()
        self.screen_height = self.winfo_screenheight()

        # Definir categorias de resolução com foco em telas muito pequenas
        if self.screen_width <= 800:
            self.categoria_resolucao = "nano"     # 800x600 ou menor - NOVO
        elif self.screen_width <= 1024:
            self.categoria_resolucao = "micro"    # 1024x768 ou menor
        elif self.screen_width <= 1280:
            self.categoria_resolucao = "mini"     # 1280x720, 1280x800
        elif self.screen_width <= 1366:
            # Para 1366x768, usar modo ultra compacto se altura for pequena
            if self.screen_height <= 768:
                self.categoria_resolucao = "mini"     # Tratar 1366x768 como mini
            else:
                self.categoria_resolucao = "pequena"  # 1366x768 com altura maior
        elif self.screen_width <= 1920:
            self.categoria_resolucao = "media"    # 1920x1080
        elif self.screen_width <= 2560:
            self.categoria_resolucao = "grande"   # 2560x1440
        else:
            self.categoria_resolucao = "ultra"    # 4K ou maior

        print(f"Resolução detectada: {self.screen_width}x{self.screen_height} - Categoria: {self.categoria_resolucao}")

        # Ativar modo ultra compacto para telas muito pequenas
        self.modo_ultra_compacto = self.categoria_resolucao in ["nano", "micro", "mini"]

        # Ativar modo nano para telas extremamente pequenas
        self.modo_nano = self.categoria_resolucao == "nano"

    def configurar_dimensoes_responsivas(self):
        """Configura dimensões baseadas na resolução detectada"""
        # Configurações por categoria de resolução com foco em telas muito pequenas
        configs = {
            "nano": {
                "janela_principal": (min(750, int(self.screen_width * 0.98)), min(500, int(self.screen_height * 0.90))),
                "janela_popup": (min(300, int(self.screen_width * 0.85)), min(200, int(self.screen_height * 0.65))),
                "janela_meditacao": (min(320, int(self.screen_width * 0.90)), min(220, int(self.screen_height * 0.70))),
                "janela_primeiro_acesso": (min(350, int(self.screen_width * 0.90)), min(250, int(self.screen_height * 0.75))),
                "notificacao": (min(200, int(self.screen_width * 0.45)), 50),
                "fonte_scale": 0.65,
                "padding_scale": 0.4
            },
            "micro": {
                "janela_principal": (min(900, int(self.screen_width * 0.95)), min(600, int(self.screen_height * 0.85))),
                "janela_popup": (min(350, int(self.screen_width * 0.8)), min(250, int(self.screen_height * 0.6))),
                "janela_meditacao": (min(380, int(self.screen_width * 0.85)), min(280, int(self.screen_height * 0.65))),
                "janela_primeiro_acesso": (min(400, int(self.screen_width * 0.85)), min(300, int(self.screen_height * 0.7))),
                "notificacao": (min(250, int(self.screen_width * 0.4)), 60),
                "fonte_scale": 0.75,
                "padding_scale": 0.6
            },
            "mini": {
                "janela_principal": (min(1100, int(self.screen_width * 0.95)), min(600, int(self.screen_height * 0.85))),
                "janela_popup": (min(400, int(self.screen_width * 0.7)), min(300, int(self.screen_height * 0.6))),
                "janela_meditacao": (min(450, int(self.screen_width * 0.75)), min(350, int(self.screen_height * 0.65))),
                "janela_primeiro_acesso": (min(480, int(self.screen_width * 0.75)), min(360, int(self.screen_height * 0.7))),
                "notificacao": (min(280, int(self.screen_width * 0.4)), 65),
                "fonte_scale": 0.85,
                "padding_scale": 0.75
            },
            "pequena": {
                "janela_principal": (1000, 700),
                "janela_popup": (400, 300),
                "janela_meditacao": (450, 350),
                "janela_primeiro_acesso": (500, 350),
                "notificacao": (280, 70),
                "fonte_scale": 0.9,
                "padding_scale": 0.8
            },
            "media": {
                "janela_principal": (1200, 800),
                "janela_popup": (500, 400),
                "janela_meditacao": (500, 400),
                "janela_primeiro_acesso": (600, 400),
                "notificacao": (300, 80),
                "fonte_scale": 1.0,
                "padding_scale": 1.0
            },
            "grande": {
                "janela_principal": (1400, 900),
                "janela_popup": (600, 500),
                "janela_meditacao": (600, 500),
                "janela_primeiro_acesso": (700, 500),
                "notificacao": (350, 90),
                "fonte_scale": 1.1,
                "padding_scale": 1.2
            },
            "ultra": {
                "janela_principal": (1600, 1000),
                "janela_popup": (700, 600),
                "janela_meditacao": (700, 600),
                "janela_primeiro_acesso": (800, 600),
                "notificacao": (400, 100),
                "fonte_scale": 1.3,
                "padding_scale": 1.4
            }
        }

        # Aplicar configurações
        self.config_responsiva = configs[self.categoria_resolucao]

        # Definir tamanho da janela principal
        width, height = self.config_responsiva["janela_principal"]
        self.geometry(f"{width}x{height}")

        # Configurar fontes responsivas
        self.configurar_fontes_responsivas()

    def configurar_fontes_responsivas(self):
        """Ajusta tamanhos de fonte baseado na resolução"""
        scale = self.config_responsiva["fonte_scale"]

        # Fontes melhoradas com escala responsiva
        self.fontes = {
            'titulo_principal': ('Segoe UI', int(24 * scale), 'bold'),
            'titulo': ('Segoe UI', int(20 * scale), 'bold'),
            'subtitulo': ('Segoe UI', int(16 * scale), 'bold'),
            'texto': ('Segoe UI', int(12 * scale)),
            'texto_pequeno': ('Segoe UI', int(10 * scale)),
            'numeros_grandes': ('Segoe UI', int(32 * scale), 'bold'),
            'numeros': ('Segoe UI', int(28 * scale), 'bold'),
            'numeros_pequenos': ('Segoe UI', int(16 * scale), 'bold'),
            'botao': ('Segoe UI', int(12 * scale), 'bold'),
            'botao_pequeno': ('Segoe UI', int(10 * scale)),
            'emoji': ('Segoe UI Emoji', int(16 * scale)),
            'emoji_grande': ('Segoe UI Emoji', int(24 * scale))
        }

    def obter_padding_responsivo(self, base_padding):
        """Retorna padding ajustado baseado na resolução"""
        return int(base_padding * self.config_responsiva["padding_scale"])

    def obter_dimensoes_card_responsivas(self, base_width, base_height):
        """Retorna dimensões de card ajustadas baseado na resolução"""
        scale = self.config_responsiva["padding_scale"]
        return int(base_width * scale), int(base_height * scale)

    def configurar_scrollbar_responsiva(self, container):
        """Configura scrollbar com dimensões responsivas"""
        # Canvas e Scrollbar responsivos
        canvas = tk.Canvas(container, bg=self.cores['bg_principal'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.cores['bg_principal'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack com dimensões responsivas
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Configurar scroll com mouse
        self.configurar_scroll_mouse(canvas)

        return scrollable_frame

    def ajustar_layout_automatico(self, event=None):
        """Ajusta o layout automaticamente quando a janela é redimensionada"""
        if hasattr(self, 'main_container'):
            # Obter dimensões atuais da janela
            current_width = self.winfo_width()

            # Ajustar elementos baseado no tamanho atual
            if current_width < 1000:
                # Layout compacto para janelas pequenas
                self.aplicar_layout_compacto()
            else:
                # Layout normal para janelas maiores
                self.aplicar_layout_normal()

    def aplicar_layout_compacto(self):
        """Aplica layout compacto para telas pequenas"""
        # Reduzir padding em elementos
        if hasattr(self, 'main_container'):
            self.main_container.configure(padx=10, pady=10)

    def aplicar_layout_normal(self):
        """Aplica layout normal para telas maiores"""
        # Restaurar padding normal
        if hasattr(self, 'main_container'):
            padding = self.obter_padding_responsivo(20)
            self.main_container.configure(padx=padding, pady=padding)

    def configurar_redimensionamento_automatico(self):
        """Configura eventos para redimensionamento automático"""
        self.bind('<Configure>', self.ajustar_layout_automatico)

    def aplicar_modo_ultra_compacto(self):
        """Aplica configurações específicas para telas muito pequenas"""
        if not self.modo_ultra_compacto:
            return

        # Reduzir ainda mais o header
        if hasattr(self, 'main_container'):
            # Configurar notebook com abas menores
            style = ttk.Style()
            if self.modo_nano:
                # Configurações extremas para modo nano
                style.configure('Nano.TNotebook.Tab', padding=[1, 0])
                style.configure('Nano.TNotebook', tabposition='n')
                if hasattr(self, 'notebook'):
                    self.notebook.configure(style='Nano.TNotebook')
            else:
                style.configure('Compact.TNotebook.Tab', padding=[2, 1])
                if hasattr(self, 'notebook'):
                    self.notebook.configure(style='Compact.TNotebook')

        # Aplicar configurações ultra compactas
        self.configurar_interface_ultra_compacta()

        # Configurar abas compactas
        self.after(100, self.configurar_abas_compactas)  # Aguardar criação das abas

    def configurar_interface_ultra_compacta(self):
        """Configura interface específica para telas muito pequenas"""
        # Reduzir padding em todos os elementos
        if self.modo_nano:
            padding_minimo = 1
        else:
            padding_minimo = 2

        # Configurar estilo compacto para botões
        self.configurar_botoes_compactos()

        # Configurar cards menores
        self.configurar_cards_compactos()

        # Aplicar modo nano se necessário
        if self.modo_nano:
            self.aplicar_modo_nano()

    def configurar_botoes_compactos(self):
        """Configura botões para modo ultra compacto"""
        # Atualizar função de criar botão moderno para modo compacto
        if self.modo_nano:
            self.padding_botao_compacto = 3
        elif self.modo_ultra_compacto:
            self.padding_botao_compacto = 5
        else:
            self.padding_botao_compacto = 10

    def configurar_cards_compactos(self):
        """Configura cards para modo ultra compacto"""
        # Reduzir altura dos cards
        if self.modo_nano:
            self.altura_card_compacto = 40
        elif self.modo_ultra_compacto:
            self.altura_card_compacto = 60
        else:
            self.altura_card_compacto = 100

    def aplicar_modo_nano(self):
        """Aplica configurações extremas para telas nano (800x600 ou menor)"""
        # Configurações específicas para modo nano

        # Reduzir ainda mais o padding do container principal
        if hasattr(self, 'main_container'):
            self.main_container.configure(padx=3, pady=3)

        # Configurar fontes ainda menores para modo nano
        self.configurar_fontes_nano()

        # Configurar cores com melhor contraste para legibilidade
        self.configurar_cores_alta_legibilidade()

    def configurar_fontes_nano(self):
        """Configura fontes específicas para modo nano"""
        # Fontes ainda menores mas legíveis
        nano_fontes = {
            'titulo_principal': ('Segoe UI', 14, 'bold'),
            'titulo': ('Segoe UI', 12, 'bold'),
            'subtitulo': ('Segoe UI', 10, 'bold'),
            'texto': ('Segoe UI', 8),
            'texto_pequeno': ('Segoe UI', 7),
            'numeros_grandes': ('Segoe UI', 18, 'bold'),
            'numeros': ('Segoe UI', 14, 'bold'),
            'numeros_pequenos': ('Segoe UI', 10, 'bold'),
            'botao': ('Segoe UI', 8, 'bold'),
            'botao_pequeno': ('Segoe UI', 7),
            'emoji': ('Segoe UI Emoji', 10),
            'emoji_grande': ('Segoe UI Emoji', 14)
        }

        # Atualizar fontes
        self.fontes.update(nano_fontes)

    def configurar_cores_alta_legibilidade(self):
        """Configura cores com melhor contraste para modo nano"""
        # Cores com contraste mais alto para melhor legibilidade
        cores_alta_legibilidade = {
            'bg_principal': '#000000',      # Fundo preto para máximo contraste
            'bg_secundario': '#1a1a1a',     # Fundo secundário muito escuro
            'card': '#2a2a2a',              # Cards escuros
            'card_hover': '#3a3a3a',        # Hover mais claro
            'texto': '#ffffff',             # Texto branco puro
            'texto_secundario': '#e0e0e0',  # Texto secundário claro
            'destaque': '#ffff00',          # Amarelo vibrante para destaque
            'destaque_hover': '#ffff80',    # Amarelo claro para hover
            'sucesso': '#00ff00',           # Verde vibrante
            'sucesso_hover': '#80ff80',     # Verde claro
            'botao': '#ffff00',             # Botões amarelos
            'botao_hover': '#ffff80',       # Hover amarelo claro
            'texto_botao': '#000000',       # Texto preto em botões amarelos
            'dourado': '#ffff00',           # Dourado como amarelo vibrante
            'dourado_claro': '#ffff80',     # Dourado claro
            'dourado_escuro': '#cccc00',    # Dourado escuro
        }

        # Aplicar apenas se modo nano estiver ativo
        if self.modo_nano:
            self.cores.update(cores_alta_legibilidade)

    def obter_colunas_grid_responsivas(self):
        """Retorna número de colunas baseado na resolução"""
        if self.categoria_resolucao == "nano":
            return 1  # Uma coluna para telas nano
        elif self.categoria_resolucao in ["micro", "mini"]:
            return 1  # Uma coluna para telas muito pequenas
        elif self.categoria_resolucao == "pequena":
            return 1  # Uma coluna para telas pequenas
        elif self.categoria_resolucao == "media":
            return 2  # Duas colunas para telas médias
        else:
            return 3  # Três colunas para telas grandes

    def criar_grid_responsivo(self, parent, itens, criar_item_func):
        """Cria um grid responsivo baseado na resolução"""
        colunas = self.obter_colunas_grid_responsivas()

        # Frame para o grid
        grid_frame = tk.Frame(parent, bg=self.cores['bg_principal'])
        grid_frame.pack(fill='x', pady=self.obter_padding_responsivo(10))

        # Configurar colunas do grid
        for i in range(colunas):
            grid_frame.grid_columnconfigure(i, weight=1)

        # Adicionar itens ao grid
        for i, item in enumerate(itens):
            row = i // colunas
            col = i % colunas
            criar_item_func(grid_frame, item, row, col)

        return grid_frame

    def ajustar_texto_responsivo(self, texto, max_chars_por_linha=None):
        """Ajusta texto baseado na resolução da tela"""
        if max_chars_por_linha is None:
            # Definir caracteres por linha baseado na resolução
            if self.categoria_resolucao == "micro":
                max_chars_por_linha = 25
            elif self.categoria_resolucao == "mini":
                max_chars_por_linha = 30
            elif self.categoria_resolucao == "pequena":
                max_chars_por_linha = 40
            elif self.categoria_resolucao == "media":
                max_chars_por_linha = 60
            else:
                max_chars_por_linha = 80

        # Quebrar texto em linhas se necessário
        palavras = texto.split()
        linhas = []
        linha_atual = ""

        for palavra in palavras:
            if len(linha_atual + " " + palavra) <= max_chars_por_linha:
                linha_atual += " " + palavra if linha_atual else palavra
            else:
                if linha_atual:
                    linhas.append(linha_atual)
                linha_atual = palavra

        if linha_atual:
            linhas.append(linha_atual)

        return "\n".join(linhas)

    def criar_efeito_premium_avancado(self):
        """Cria efeitos visuais avançados para o título premium"""
        # Adicionar efeito de pulsação sutil
        self.after(500, self.iniciar_pulsacao_premium)

    def iniciar_pulsacao_premium(self):
        """Inicia efeito de pulsação no título premium"""
        try:
            # Efeito sutil de pulsação a cada 4 segundos
            self.after(4000, self.iniciar_pulsacao_premium)
        except:
            pass

    def configurar_abas_compactas(self):
        """Configura abas com nomes legíveis para diferentes tamanhos de tela"""
        try:
            # Obter todas as abas
            tabs = self.notebook.tabs()

            # Definir nomes baseados no modo
            if self.modo_nano:
                # Nomes ultra compactos para modo nano
                nomes_abas = [
                    "🏠 Home",        # Principal
                    "👁️ Ver",         # Visualização
                    "🧘‍♀️ Med",        # Meditação
                    "🙏 Grat",        # Gratidão
                    "💰 Inv",         # Investimentos
                    "✨ Vida",        # Surpresas
                    "🎁 Pres",        # Presentes
                    "🏆 Conq",        # Conquistas
                    "🛒 Loja",        # Loja
                    "📊 Stats"        # Dashboard
                ]
            elif self.modo_ultra_compacto:
                # Nomes compactos mas legíveis
                nomes_abas = [
                    "🏠 Principal",   # Principal
                    "👁️ Visual",      # Visualização
                    "🧘‍♀️ Meditar",   # Meditação
                    "🙏 Gratidão",    # Gratidão
                    "💰 Invest",      # Investimentos
                    "✨ Surpresas",   # Surpresas
                    "🎁 Presentes",   # Presentes
                    "🏆 Conquistas",  # Conquistas
                    "🛒 Loja",        # Loja
                    "📊 Dashboard"    # Dashboard
                ]
            else:
                # Nomes completos para telas normais
                nomes_abas = [
                    "🏠 Principal",
                    "👁️ Visualização",
                    "🧘‍♀️ Meditação",
                    "🙏 Gratidão",
                    "💰 Investimentos",
                    "✨ Vida me Surpreenda",
                    "🎁 Presentes do Universo",
                    "🏆 Conquistas",
                    "🛒 Loja",
                    "📊 Dashboard"
                ]

            # Aplicar nomes às abas
            for i, tab in enumerate(tabs):
                if i < len(nomes_abas):
                    self.notebook.tab(tab, text=nomes_abas[i])
        except:
            pass  # Ignorar erros se as abas ainda não existirem

    def otimizar_para_1366x768(self):
        """Otimizações específicas para resolução 1366x768"""
        if self.screen_width == 1366 and self.screen_height == 768:
            # Configurações específicas para essa resolução comum

            # Reduzir altura do header ainda mais
            if hasattr(self, 'main_container'):
                # Forçar padding mínimo
                self.main_container.configure(padx=8, pady=8)

            # Configurar notebook com abas menores
            try:
                style = ttk.Style()
                style.configure('Tiny.TNotebook.Tab', padding=[3, 1])
                style.configure('Tiny.TNotebook', tabposition='n')
                if hasattr(self, 'notebook'):
                    self.notebook.configure(style='Tiny.TNotebook')
            except:
                pass

            # Reduzir tamanho mínimo da janela
            self.minsize(1000, 550)

            # Aplicar configurações de fonte ainda menores
            scale = 0.8
            self.fontes = {
                'titulo_principal': ('Segoe UI', int(20 * scale), 'bold'),
                'titulo': ('Segoe UI', int(16 * scale), 'bold'),
                'subtitulo': ('Segoe UI', int(14 * scale), 'bold'),
                'texto': ('Segoe UI', int(10 * scale)),
                'texto_pequeno': ('Segoe UI', int(9 * scale)),
                'numeros_grandes': ('Segoe UI', int(24 * scale), 'bold'),
                'numeros': ('Segoe UI', int(20 * scale), 'bold'),
                'numeros_pequenos': ('Segoe UI', int(14 * scale), 'bold'),
                'botao': ('Segoe UI', int(10 * scale), 'bold'),
                'botao_pequeno': ('Segoe UI', int(9 * scale)),
                'emoji': ('Segoe UI Emoji', int(12 * scale)),
                'emoji_grande': ('Segoe UI Emoji', int(16 * scale))
            }

            # Forçar atualização da interface
            self.after(50, self.aplicar_espacamento_minimo)

    def aplicar_espacamento_minimo(self):
        """Aplica espaçamento mínimo em todos os elementos"""
        try:
            # Reduzir espaçamento do notebook
            if hasattr(self, 'notebook'):
                self.notebook.pack_configure(pady=2)

            # Reduzir espaçamento do card principal
            if hasattr(self, 'main_container'):
                for child in self.main_container.winfo_children():
                    try:
                        child.pack_configure(pady=2)
                    except:
                        pass
        except:
            pass

    def criar_menu_zoom(self):
        """Cria menu avançado para ajuste de zoom e acessibilidade"""
        menubar = tk.Menu(self)
        self.config(menu=menubar)

        # Menu de visualização
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Visualização", menu=view_menu)

        # Submenu de zoom
        zoom_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="Zoom", menu=zoom_menu)

        zoom_menu.add_command(label="50% (Mínimo)", command=lambda: self.aplicar_zoom(0.5))
        zoom_menu.add_command(label="75% (Pequeno)", command=lambda: self.aplicar_zoom(0.75))
        zoom_menu.add_command(label="90% (Compacto)", command=lambda: self.aplicar_zoom(0.9))
        zoom_menu.add_command(label="100% (Normal)", command=lambda: self.aplicar_zoom(1.0))
        zoom_menu.add_command(label="110% (Confortável)", command=lambda: self.aplicar_zoom(1.1))
        zoom_menu.add_command(label="125% (Grande)", command=lambda: self.aplicar_zoom(1.25))
        zoom_menu.add_command(label="150% (Muito Grande)", command=lambda: self.aplicar_zoom(1.5))
        zoom_menu.add_command(label="200% (Máximo)", command=lambda: self.aplicar_zoom(2.0))
        zoom_menu.add_separator()
        zoom_menu.add_command(label="Zoom In (+)", command=self.zoom_in)
        zoom_menu.add_command(label="Zoom Out (-)", command=self.zoom_out)
        zoom_menu.add_command(label="Reset Zoom", command=self.reset_zoom)

        # Submenu de acessibilidade
        access_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="Acessibilidade", menu=access_menu)

        access_menu.add_command(label="Alto Contraste", command=self.toggle_alto_contraste)
        access_menu.add_command(label="Modo Nano", command=self.toggle_modo_nano)
        access_menu.add_command(label="Modo Ultra Compacto", command=self.toggle_modo_ultra_compacto)
        access_menu.add_separator()
        access_menu.add_command(label="Salvar Preferências", command=self.salvar_preferencias_zoom)
        access_menu.add_command(label="Restaurar Padrão", command=self.restaurar_padrao_zoom)

        # Atalhos de teclado
        self.bind_all("<Control-plus>", lambda _: self.zoom_in())
        self.bind_all("<Control-minus>", lambda _: self.zoom_out())
        self.bind_all("<Control-0>", lambda _: self.reset_zoom())

    def aplicar_zoom(self, fator):
        """Aplica fator de zoom à interface"""
        # Validar limites
        fator = max(self.zoom_min, min(self.zoom_max, fator))
        self.zoom_level = fator

        # Atualizar escala de fonte
        self.config_responsiva["fonte_scale"] = fator
        self.config_responsiva["padding_scale"] = fator * 0.8

        # Reconfigurar fontes
        self.configurar_fontes_responsivas()

        # Atualizar interface se já foi criada
        if hasattr(self, 'main_container'):
            self.atualizar_interface_zoom()

        # Mostrar notificação
        self.criar_notificacao(
            "🔍 Zoom Aplicado",
            f"Interface ajustada para {int(fator * 100)}%",
            tipo="info",
            duracao=3000
        )

    def zoom_in(self):
        """Aumenta o zoom"""
        novo_zoom = min(self.zoom_max, self.zoom_level + self.zoom_step)
        self.aplicar_zoom(novo_zoom)

    def zoom_out(self):
        """Diminui o zoom"""
        novo_zoom = max(self.zoom_min, self.zoom_level - self.zoom_step)
        self.aplicar_zoom(novo_zoom)

    def reset_zoom(self):
        """Reseta o zoom para 100%"""
        self.aplicar_zoom(1.0)

    def toggle_alto_contraste(self):
        """Alterna modo de alto contraste"""
        if not hasattr(self, 'alto_contraste_ativo'):
            self.alto_contraste_ativo = False

        self.alto_contraste_ativo = not self.alto_contraste_ativo

        if self.alto_contraste_ativo:
            self.aplicar_alto_contraste()
            self.criar_notificacao(
                "🎨 Alto Contraste",
                "Ativado para melhor legibilidade",
                tipo="sucesso"
            )
        else:
            self.restaurar_cores_normais()
            self.criar_notificacao(
                "🎨 Cores Normais",
                "Alto contraste desativado",
                tipo="info"
            )

    def toggle_modo_nano(self):
        """Alterna modo nano manualmente"""
        self.modo_nano = not self.modo_nano

        if self.modo_nano:
            self.aplicar_modo_nano()
            self.criar_notificacao(
                "📱 Modo Nano",
                "Ativado para telas muito pequenas",
                tipo="sucesso"
            )
        else:
            self.restaurar_cores_normais()
            self.criar_notificacao(
                "🖥️ Modo Normal",
                "Modo nano desativado",
                tipo="info"
            )

    def toggle_modo_ultra_compacto(self):
        """Alterna modo ultra compacto manualmente"""
        self.modo_ultra_compacto = not self.modo_ultra_compacto

        if self.modo_ultra_compacto:
            self.aplicar_modo_ultra_compacto()
            self.criar_notificacao(
                "📱 Modo Ultra Compacto",
                "Ativado para telas pequenas",
                tipo="sucesso"
            )
        else:
            self.criar_notificacao(
                "🖥️ Modo Normal",
                "Interface expandida",
                tipo="info"
            )

    def aplicar_alto_contraste(self):
        """Aplica cores de alto contraste"""
        cores_alto_contraste = {
            'bg_principal': '#000000',
            'bg_secundario': '#1a1a1a',
            'card': '#2a2a2a',
            'card_hover': '#3a3a3a',
            'texto': '#ffffff',
            'texto_secundario': '#e0e0e0',
            'destaque': '#ffff00',
            'destaque_hover': '#ffff80',
            'sucesso': '#00ff00',
            'sucesso_hover': '#80ff80',
            'botao': '#ffff00',
            'botao_hover': '#ffff80',
            'texto_botao': '#000000',
            'dourado': '#ffff00',
            'dourado_claro': '#ffff80',
            'dourado_escuro': '#cccc00',
        }

        # Salvar cores originais se não foram salvas
        if not hasattr(self, 'cores_originais'):
            self.cores_originais = self.cores.copy()

        self.cores.update(cores_alto_contraste)
        self.atualizar_interface_cores()

    def restaurar_cores_normais(self):
        """Restaura cores normais"""
        if hasattr(self, 'cores_originais'):
            self.cores = self.cores_originais.copy()
            self.atualizar_interface_cores()

    def atualizar_interface_zoom(self):
        """Atualiza interface após mudança de zoom"""
        # Recriar elementos principais se necessário
        try:
            if hasattr(self, 'saldo_label'):
                self.saldo_label.configure(font=self.fontes['numeros'])
            if hasattr(self, 'nivel_label'):
                self.nivel_label.configure(font=self.fontes['texto'])
        except:
            pass

    def atualizar_interface_cores(self):
        """Atualiza cores da interface"""
        try:
            # Atualizar cor de fundo principal
            self.configure(bg=self.cores['bg_principal'])
            if hasattr(self, 'main_container'):
                self.main_container.configure(bg=self.cores['bg_principal'])
        except:
            pass

    def salvar_preferencias_zoom(self):
        """Salva preferências de zoom e acessibilidade"""
        self.zoom_preferences = {
            'zoom_level': self.zoom_level,
            'alto_contraste': getattr(self, 'alto_contraste_ativo', False),
            'modo_nano': self.modo_nano,
            'modo_ultra_compacto': self.modo_ultra_compacto
        }

        # Salvar junto com dados do jogo
        self.salvar_progresso()

        self.criar_notificacao(
            "💾 Preferências Salvas",
            "Configurações de zoom e acessibilidade salvas",
            tipo="sucesso"
        )

    def restaurar_padrao_zoom(self):
        """Restaura configurações padrão de zoom"""
        self.reset_zoom()
        if hasattr(self, 'alto_contraste_ativo') and self.alto_contraste_ativo:
            self.toggle_alto_contraste()

        self.criar_notificacao(
            "🔄 Padrão Restaurado",
            "Configurações de zoom resetadas",
            tipo="info"
        )

    def iniciar_meditacao(self, tipo):
        """Inicia uma sessão de meditação"""
        if self.meditacao_ativa:
            self.mostrar_mensagem("⚠️ Atenção", "Você já está em uma sessão de meditação!")
            return

        self.meditacao_ativa = True
        self.tipo_meditacao_atual = tipo
        self.tempo_meditacao_atual = tipo['tempo'] * 60  # Converter para segundos

        # Criar janela de meditação com dimensões responsivas
        self.janela_meditacao = tk.Toplevel(self)
        self.janela_meditacao.title(f"🧘‍♀️ {tipo['nome']}")
        self.janela_meditacao.configure(bg=self.cores['bg_principal'])
        self.janela_meditacao.resizable(True, True)

        # Calcular dimensões baseadas na resolução
        if self.modo_nano:
            width = min(320, int(self.screen_width * 0.95))
            height = min(220, int(self.screen_height * 0.85))
        elif self.modo_ultra_compacto:
            width = min(400, int(self.screen_width * 0.9))
            height = min(300, int(self.screen_height * 0.8))
        else:
            width, height = self.config_responsiva["janela_meditacao"]

        # Centralizar janela
        self.centralizar_janela(self.janela_meditacao, width, height)

        # Impedir fechamento durante meditação
        self.janela_meditacao.protocol("WM_DELETE_WINDOW", self.confirmar_parar_meditacao)

        # Header da janela
        header = tk.Frame(self.janela_meditacao, bg=self.cores['card'], height=80)
        header.pack(fill='x', padx=10, pady=10)
        header.pack_propagate(False)

        tk.Label(
            header,
            text=tipo['nome'],
            font=self.fontes['titulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(expand=True)

        tk.Label(
            header,
            text=tipo['desc'],
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack()

        # Timer circular
        timer_frame = tk.Frame(self.janela_meditacao, bg=self.cores['bg_principal'])
        timer_frame.pack(expand=True, fill='both', padx=20, pady=20)

        # Container do timer
        timer_container = tk.Frame(timer_frame, bg=self.cores['bg_principal'])
        timer_container.pack(expand=True)

        # Timer display
        timer_bg = tk.Frame(
            timer_container,
            bg=self.cores['card'],
            width=200,
            height=200
        )
        timer_bg.pack(expand=True)
        timer_bg.pack_propagate(False)

        self.timer_meditacao_label = tk.Label(
            timer_bg,
            text=self.formatar_tempo(self.tempo_meditacao_atual),
            font=self.fontes['numeros_grandes'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        )
        self.timer_meditacao_label.pack(expand=True)

        # Botões de controle
        controles_frame = tk.Frame(self.janela_meditacao, bg=self.cores['bg_principal'])
        controles_frame.pack(fill='x', padx=20, pady=(0, 20))

        self.btn_pausar = self.criar_botao_moderno(
            controles_frame,
            "⏸️ Pausar",
            self.pausar_meditacao,
            tipo="secundario"
        )
        self.btn_pausar.pack(side='left', padx=5)

        self.btn_parar = self.criar_botao_moderno(
            controles_frame,
            "⏹️ Parar",
            self.confirmar_parar_meditacao,
            tipo="erro"
        )
        self.btn_parar.pack(side='right', padx=5)

        # Iniciar contagem regressiva
        self.meditacao_pausada = False
        self.atualizar_timer_meditacao()

    def formatar_tempo(self, segundos):
        """Formata tempo em MM:SS"""
        minutos = segundos // 60
        segundos = segundos % 60
        return f"{minutos:02d}:{segundos:02d}"

    def atualizar_timer_meditacao(self):
        """Atualiza o timer da meditação"""
        if not self.meditacao_ativa or self.meditacao_pausada:
            return

        if self.tempo_meditacao_atual > 0:
            self.timer_meditacao_label.config(text=self.formatar_tempo(self.tempo_meditacao_atual))
            self.tempo_meditacao_atual -= 1
            self.after(1000, self.atualizar_timer_meditacao)
        else:
            self.finalizar_meditacao()

    def pausar_meditacao(self):
        """Pausa ou retoma a meditação"""
        if self.meditacao_pausada:
            self.meditacao_pausada = False
            self.btn_pausar.config(text="⏸️ Pausar")
            self.atualizar_timer_meditacao()
        else:
            self.meditacao_pausada = True
            self.btn_pausar.config(text="▶️ Retomar")

    def confirmar_parar_meditacao(self):
        """Confirma se o usuário quer parar a meditação"""
        if messagebox.askyesno("Parar Meditação", "Tem certeza que deseja parar a meditação?"):
            self.parar_meditacao()

    def parar_meditacao(self):
        """Para a meditação sem recompensas"""
        self.meditacao_ativa = False
        self.meditacao_pausada = False
        if hasattr(self, 'janela_meditacao'):
            self.janela_meditacao.destroy()

    def finalizar_meditacao(self):
        """Finaliza a meditação com recompensas"""
        tipo = self.tipo_meditacao_atual
        tempo_total = tipo['tempo']

        # Atualizar estatísticas
        self.total_meditado += 1
        self.tempo_total_meditado += tempo_total

        # Calcular recompensas
        bonus_min, bonus_max = tipo['bonus']
        bonus = random.randint(bonus_min, bonus_max)
        xp_bonus = tempo_total * 10  # 10 XP por minuto

        self.saldo += bonus
        self.xp += xp_bonus

        # Atualizar interface
        self.atualizar_interface()
        self.label_total_meditado.config(text=f"{self.total_meditado} sessões")
        self.label_tempo_total.config(text=f"{self.tempo_total_meditado} min")

        # Verificar conquistas
        self.verificar_conquistas()

        # Salvar progresso
        self.salvar_progresso()

        # Fechar janela
        self.meditacao_ativa = False
        if hasattr(self, 'janela_meditacao'):
            self.janela_meditacao.destroy()

        # Som de conclusão
        try:
            winsound.Beep(800, 300)
            winsound.Beep(1000, 300)
            winsound.Beep(1200, 500)
        except:
            pass

        # Mostrar recompensas
        self.mostrar_mensagem(
            "🧘‍♀️ Meditação Concluída",
            f"Parabéns! Você completou {tempo_total} minutos de {tipo['nome']}!\n\n"
            f"Recompensas:\n"
            f"💰 R$ {bonus:,.2f}\n"
            f"⭐ {xp_bonus} XP\n\n"
            f"Total de sessões: {self.total_meditado}\n"
            f"Tempo total: {self.tempo_total_meditado} minutos"
        )

    def criar_aba_loja(self):
        """Nova aba da loja virtual"""
        aba_loja = tk.Frame(
            self.notebook,
            bg=self.cores['bg_principal']
        )
        self.notebook.add(aba_loja, text="🛒 Loja")

        # Container principal com scroll responsivo
        container = tk.Frame(aba_loja, bg=self.cores['bg_principal'])
        container.pack(fill='both', expand=True)

        # Usar função responsiva para scrollbar
        scrollable_frame = self.configurar_scrollbar_responsiva(container)

        # Header da loja com padding responsivo
        padding = self.obter_padding_responsivo(20)
        header_frame = tk.Frame(scrollable_frame, bg=self.cores['bg_principal'])
        header_frame.pack(fill='x', padx=padding, pady=padding)

        # Card do header
        header_card = tk.Frame(
            header_frame,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        header_card.pack(fill='x')

        header_content = tk.Frame(header_card, bg=self.cores['card'])
        header_content.pack(fill='x', padx=30, pady=20)

        # Título da loja
        titulo_frame = tk.Frame(header_content, bg=self.cores['card'])
        titulo_frame.pack()

        tk.Label(
            titulo_frame,
            text="🛒",
            font=self.fontes['emoji_grande'],
            bg=self.cores['card'],
            fg=self.cores['destaque']
        ).pack(side='left')

        tk.Label(
            titulo_frame,
            text=" Loja do Universo",
            font=self.fontes['titulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left')

        # Saldo de moedas especiais
        saldo_frame = tk.Frame(header_content, bg=self.cores['card'])
        saldo_frame.pack(pady=(10, 0))

        tk.Label(
            saldo_frame,
            text="💎",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['diamante']
        ).pack(side='left')

        self.label_moedas = tk.Label(
            saldo_frame,
            text=f" Cristais Universais: {self.moedas_especiais}",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        )
        self.label_moedas.pack(side='left')

        # Seções da loja
        self.criar_secao_loja(scrollable_frame, "🎯 Power-ups", self.get_powerups())
        self.criar_secao_loja(scrollable_frame, "🎨 Personalizações", self.get_personalizacoes())
        self.criar_secao_loja(scrollable_frame, "🎁 Itens Especiais", self.get_itens_especiais())

    def get_powerups(self):
        """Retorna lista de power-ups disponíveis"""
        return [
            {
                "nome": "⚡ Multiplicador 2x",
                "descricao": "Dobra as recompensas por 1 hora",
                "preco": 50,
                "tipo": "powerup",
                "duracao": 60,
                "efeito": "multiplicador_2x"
            },
            {
                "nome": "🌟 XP Boost",
                "descricao": "Triplica o XP ganho por 30 min",
                "preco": 30,
                "tipo": "powerup",
                "duracao": 30,
                "efeito": "xp_boost_3x"
            },
            {
                "nome": "🍀 Sorte Dourada",
                "descricao": "Aumenta chances de surpresas por 2 horas",
                "preco": 75,
                "tipo": "powerup",
                "duracao": 120,
                "efeito": "sorte_dourada"
            },
            {
                "nome": "💰 Chuva de Dinheiro",
                "descricao": "Ganhe R$ 100.000 instantaneamente",
                "preco": 25,
                "tipo": "instantaneo",
                "efeito": "dinheiro_instantaneo"
            }
        ]

    def get_personalizacoes(self):
        """Retorna lista de personalizações disponíveis"""
        return [
            {
                "nome": "🌈 Tema Arco-íris",
                "descricao": "Cores vibrantes e alegres",
                "preco": 100,
                "tipo": "tema",
                "efeito": "tema_arcoiris"
            },
            {
                "nome": "🌙 Tema Noturno",
                "descricao": "Cores escuras e elegantes",
                "preco": 80,
                "tipo": "tema",
                "efeito": "tema_noturno"
            },
            {
                "nome": "👑 Título VIP",
                "descricao": "Exiba 'Manifestador VIP' no seu perfil",
                "preco": 200,
                "tipo": "titulo",
                "efeito": "titulo_vip"
            },
            {
                "nome": "✨ Efeitos Especiais",
                "descricao": "Animações e partículas extras",
                "preco": 150,
                "tipo": "efeito",
                "efeito": "efeitos_especiais"
            }
        ]

    def get_itens_especiais(self):
        """Retorna lista de itens especiais disponíveis"""
        return [
            {
                "nome": "🔮 Bola de Cristal",
                "descricao": "Revela uma surpresa futura",
                "preco": 120,
                "tipo": "especial",
                "efeito": "revelar_surpresa"
            },
            {
                "nome": "🧿 Amuleto da Sorte",
                "descricao": "Proteção contra perdas por 7 dias",
                "preco": 300,
                "tipo": "especial",
                "duracao": 10080,  # 7 dias em minutos
                "efeito": "protecao_perdas"
            },
            {
                "nome": "📜 Pergaminho Antigo",
                "descricao": "Desbloqueia uma conquista aleatória",
                "preco": 500,
                "tipo": "especial",
                "efeito": "desbloquear_conquista"
            },
            {
                "nome": "💎 Cristal do Infinito",
                "descricao": "Gera 10 cristais por dia automaticamente",
                "preco": 1000,
                "tipo": "especial",
                "efeito": "gerador_cristais"
            }
        ]

    def criar_secao_loja(self, parent, titulo, itens):
        """Cria uma seção da loja com seus itens"""
        secao_frame = tk.Frame(parent, bg=self.cores['bg_principal'])
        secao_frame.pack(fill='x', padx=20, pady=10)

        # Título da seção
        titulo_frame = tk.Frame(secao_frame, bg=self.cores['bg_principal'])
        titulo_frame.pack(fill='x', pady=(0, 15))

        tk.Label(
            titulo_frame,
            text=titulo,
            font=self.fontes['subtitulo'],
            bg=self.cores['bg_principal'],
            fg=self.cores['destaque']
        ).pack()

        # Grid de itens
        grid_frame = tk.Frame(secao_frame, bg=self.cores['bg_principal'])
        grid_frame.pack(fill='x')

        for i, item in enumerate(itens):
            row = i // 2
            col = i % 2

            self.criar_card_item_loja(grid_frame, item, row, col)

        # Configurar grid
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(1, weight=1)

    def criar_card_item_loja(self, parent, item, row, col):
        """Cria um card para um item da loja"""
        card_container = tk.Frame(parent, bg=self.cores['bg_principal'])
        card_container.grid(row=row, column=col, padx=10, pady=8, sticky='ew')

        # Card do item
        card = tk.Frame(
            card_container,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        card.pack(fill='both', expand=True, padx=5, pady=5)

        # Conteúdo do card
        content_frame = tk.Frame(card, bg=self.cores['card'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=15)

        # Nome do item
        tk.Label(
            content_frame,
            text=item['nome'],
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(anchor='w')

        # Descrição
        tk.Label(
            content_frame,
            text=item['descricao'],
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario'],
            wraplength=200
        ).pack(anchor='w', pady=(5, 10))

        # Preço e botão
        bottom_frame = tk.Frame(content_frame, bg=self.cores['card'])
        bottom_frame.pack(fill='x')

        # Preço
        preco_frame = tk.Frame(bottom_frame, bg=self.cores['card'])
        preco_frame.pack(side='left')

        tk.Label(
            preco_frame,
            text="💎",
            font=self.fontes['emoji'],
            bg=self.cores['card'],
            fg=self.cores['diamante']
        ).pack(side='left')

        tk.Label(
            preco_frame,
            text=f" {item['preco']}",
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left')

        # Botão comprar
        btn_comprar = self.criar_botao_moderno(
            bottom_frame,
            "Comprar",
            lambda i=item: self.comprar_item(i),
            tipo="primario",
            font=self.fontes['texto_pequeno']
        )
        btn_comprar.pack(side='right')

        # Verificar se já foi comprado
        if item.get('id') in self.itens_comprados:
            btn_comprar.config(text="✓ Comprado", state='disabled')

    def comprar_item(self, item):
        """Compra um item da loja"""
        if self.moedas_especiais < item['preco']:
            self.mostrar_mensagem(
                "💎 Cristais Insuficientes",
                f"Você precisa de {item['preco']} cristais para comprar este item.\n"
                f"Você tem apenas {self.moedas_especiais} cristais."
            )
            return

        # Confirmar compra
        if not messagebox.askyesno(
            "Confirmar Compra",
            f"Deseja comprar {item['nome']} por {item['preco']} cristais?"
        ):
            return

        # Realizar compra
        self.moedas_especiais -= item['preco']
        self.aplicar_efeito_item(item)

        # Atualizar interface
        self.label_moedas.config(text=f" Cristais Universais: {self.moedas_especiais}")

        # Salvar progresso
        self.salvar_progresso()

        self.mostrar_mensagem(
            "🛒 Compra Realizada",
            f"Você comprou {item['nome']}!\n\n{item['descricao']}"
        )

    def aplicar_efeito_item(self, item):
        """Aplica o efeito do item comprado"""
        efeito = item['efeito']

        if efeito == "dinheiro_instantaneo":
            self.saldo += 100000
            self.atualizar_interface()
        elif efeito == "multiplicador_2x":
            # Implementar sistema de power-ups temporários
            pass
        elif efeito == "revelar_surpresa":
            # Mostrar uma surpresa futura
            surpresa = random.choice(list(self.surpresas.values())[0])
            self.mostrar_mensagem(
                "🔮 Visão do Futuro",
                f"A bola de cristal revela que você receberá:\n{surpresa}"
            )
        # Adicionar mais efeitos conforme necessário

    def criar_aba_dashboard(self):
        """Nova aba do dashboard com estatísticas avançadas"""
        aba_dashboard = tk.Frame(
            self.notebook,
            bg=self.cores['bg_principal']
        )
        self.notebook.add(aba_dashboard, text="📊 Dashboard")

        # Usar a função centralizada de scroll
        scrollable_frame = self.criar_scroll_frame(aba_dashboard)

        # Header do dashboard
        header_frame = tk.Frame(scrollable_frame, bg=self.cores['bg_principal'])
        header_frame.pack(fill='x', padx=20, pady=20)

        header_card = tk.Frame(
            header_frame,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        header_card.pack(fill='x')

        header_content = tk.Frame(header_card, bg=self.cores['card'])
        header_content.pack(fill='x', padx=30, pady=20)

        # Título
        titulo_frame = tk.Frame(header_content, bg=self.cores['card'])
        titulo_frame.pack()

        tk.Label(
            titulo_frame,
            text="📊",
            font=self.fontes['emoji_grande'],
            bg=self.cores['card'],
            fg=self.cores['destaque']
        ).pack(side='left')

        tk.Label(
            titulo_frame,
            text=" Dashboard de Manifestação",
            font=self.fontes['titulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left')

        # Resumo geral
        self.criar_resumo_geral(scrollable_frame)

        # Gráficos e estatísticas
        self.criar_graficos_stats(scrollable_frame)

        # Conquistas recentes
        self.criar_conquistas_recentes(scrollable_frame)

        # Metas e objetivos
        self.criar_metas_objetivos(scrollable_frame)

    def criar_resumo_geral(self, parent):
        """Cria o resumo geral das estatísticas"""
        resumo_frame = tk.Frame(parent, bg=self.cores['bg_principal'])
        resumo_frame.pack(fill='x', padx=20, pady=10)

        # Título da seção
        tk.Label(
            resumo_frame,
            text="📈 Resumo Geral",
            font=self.fontes['subtitulo'],
            bg=self.cores['bg_principal'],
            fg=self.cores['destaque']
        ).pack(pady=(0, 15))

        # Grid de estatísticas
        stats_grid = tk.Frame(resumo_frame, bg=self.cores['bg_principal'])
        stats_grid.pack(fill='x')

        # Estatísticas principais
        stats = [
            {"titulo": "💰 Patrimônio", "valor": f"R$ {self.saldo:,.2f}", "cor": self.cores['dourado']},
            {"titulo": "⭐ Nível", "valor": str(self.nivel), "cor": self.cores['destaque']},
            {"titulo": "🧘‍♀️ Meditações", "valor": str(self.total_meditado), "cor": self.cores['sucesso']},
            {"titulo": "👁️ Visualizações", "valor": str(self.visualizacoes_hoje), "cor": self.cores['info']},
            {"titulo": "🙏 Gratidões", "valor": str(self.gratidoes_registradas), "cor": self.cores['prata']},
            {"titulo": "💫 Afirmações", "valor": str(self.afirmacoes_ativadas), "cor": self.cores['bronze']}
        ]

        for i, stat in enumerate(stats):
            row = i // 3
            col = i % 3

            self.criar_card_estatistica(stats_grid, stat, row, col)

        # Configurar grid
        for i in range(3):
            stats_grid.grid_columnconfigure(i, weight=1)

    def criar_card_estatistica(self, parent, stat, row, col):
        """Cria um card de estatística"""
        card_container = tk.Frame(parent, bg=self.cores['bg_principal'])
        card_container.grid(row=row, column=col, padx=10, pady=8, sticky='ew')

        card = tk.Frame(
            card_container,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        card.pack(fill='both', expand=True, padx=5, pady=5)

        content_frame = tk.Frame(card, bg=self.cores['card'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=15)

        # Título
        tk.Label(
            content_frame,
            text=stat['titulo'],
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack()

        # Valor
        tk.Label(
            content_frame,
            text=stat['valor'],
            font=self.fontes['numeros_pequenos'],
            bg=self.cores['card'],
            fg=stat['cor']
        ).pack(pady=(5, 0))

    def criar_graficos_stats(self, parent):
        """Cria seção com gráficos simulados"""
        graficos_frame = tk.Frame(parent, bg=self.cores['bg_principal'])
        graficos_frame.pack(fill='x', padx=20, pady=20)

        # Título
        tk.Label(
            graficos_frame,
            text="📊 Análise de Performance",
            font=self.fontes['subtitulo'],
            bg=self.cores['bg_principal'],
            fg=self.cores['destaque']
        ).pack(pady=(0, 15))

        # Card do gráfico
        grafico_card = tk.Frame(
            graficos_frame,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        grafico_card.pack(fill='x')

        grafico_content = tk.Frame(grafico_card, bg=self.cores['card'])
        grafico_content.pack(fill='x', padx=30, pady=20)

        # Simulação de gráfico de barras
        tk.Label(
            grafico_content,
            text="📈 Progresso Semanal",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(pady=(0, 15))

        # Barras simuladas
        dias = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom']
        valores = [80, 65, 90, 75, 95, 60, 85]  # Valores simulados

        barras_frame = tk.Frame(grafico_content, bg=self.cores['card'])
        barras_frame.pack()

        for i, (dia, valor) in enumerate(zip(dias, valores)):
            barra_container = tk.Frame(barras_frame, bg=self.cores['card'])
            barra_container.pack(side='left', padx=5)

            # Barra
            barra_bg = tk.Frame(
                barra_container,
                bg=self.cores['progresso_bg'],
                width=30,
                height=100
            )
            barra_bg.pack()
            barra_bg.pack_propagate(False)

            # Preenchimento da barra
            altura_barra = int(valor)
            barra_fill = tk.Frame(
                barra_bg,
                bg=self.cores['progresso'],
                height=altura_barra
            )
            barra_fill.pack(side='bottom', fill='x')

            # Label do dia
            tk.Label(
                barra_container,
                text=dia,
                font=self.fontes['texto_pequeno'],
                bg=self.cores['card'],
                fg=self.cores['texto_secundario']
            ).pack(pady=(5, 0))

    def criar_conquistas_recentes(self, parent):
        """Cria seção de conquistas recentes"""
        conquistas_frame = tk.Frame(parent, bg=self.cores['bg_principal'])
        conquistas_frame.pack(fill='x', padx=20, pady=10)

        # Título
        tk.Label(
            conquistas_frame,
            text="🏆 Conquistas Recentes",
            font=self.fontes['subtitulo'],
            bg=self.cores['bg_principal'],
            fg=self.cores['destaque']
        ).pack(pady=(0, 15))

        # Card das conquistas
        conquistas_card = tk.Frame(
            conquistas_frame,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        conquistas_card.pack(fill='x')

        conquistas_content = tk.Frame(conquistas_card, bg=self.cores['card'])
        conquistas_content.pack(fill='x', padx=30, pady=20)

        # Listar conquistas desbloqueadas
        conquistas_desbloqueadas = [
            conquista for conquista in self.conquistas.values()
            if conquista.get('desbloqueada', False)
        ]

        if conquistas_desbloqueadas:
            for conquista in conquistas_desbloqueadas[-3:]:  # Últimas 3
                conquista_frame = tk.Frame(conquistas_content, bg=self.cores['card'])
                conquista_frame.pack(fill='x', pady=5)

                tk.Label(
                    conquista_frame,
                    text=conquista['badge'],
                    font=self.fontes['emoji'],
                    bg=self.cores['card'],
                    fg=self.cores['dourado']
                ).pack(side='left')

                tk.Label(
                    conquista_frame,
                    text=f" {conquista['nome']}",
                    font=self.fontes['texto'],
                    bg=self.cores['card'],
                    fg=self.cores['texto']
                ).pack(side='left')
        else:
            tk.Label(
                conquistas_content,
                text="Nenhuma conquista desbloqueada ainda.\nContinue manifestando para desbloquear!",
                font=self.fontes['texto'],
                bg=self.cores['card'],
                fg=self.cores['texto_secundario'],
                justify='center'
            ).pack()

    def criar_metas_objetivos(self, parent):
        """Cria seção de metas e objetivos"""
        metas_frame = tk.Frame(parent, bg=self.cores['bg_principal'])
        metas_frame.pack(fill='x', padx=20, pady=20)

        # Título
        tk.Label(
            metas_frame,
            text="🎯 Próximas Metas",
            font=self.fontes['subtitulo'],
            bg=self.cores['bg_principal'],
            fg=self.cores['destaque']
        ).pack(pady=(0, 15))

        # Card das metas
        metas_card = tk.Frame(
            metas_frame,
            bg=self.cores['card'],
            relief='flat',
            bd=0
        )
        metas_card.pack(fill='x')

        metas_content = tk.Frame(metas_card, bg=self.cores['card'])
        metas_content.pack(fill='x', padx=30, pady=20)

        # Metas baseadas no progresso atual
        metas = []

        # Meta de nível
        proximo_nivel = self.nivel + 1
        xp_necessario = self.xp_proximo_nivel - self.xp
        progresso_nivel = (self.xp / self.xp_proximo_nivel) * 100
        metas.append({
            "titulo": f"⭐ Alcançar Nível {proximo_nivel}",
            "progresso": progresso_nivel,
            "texto": f"{xp_necessario} XP restantes"
        })

        # Meta de saldo
        if self.saldo < 1000000:
            meta_saldo = 1000000
            progresso_saldo = (self.saldo / meta_saldo) * 100
            faltam = meta_saldo - self.saldo
            metas.append({
                "titulo": "💰 Primeiro Milhão",
                "progresso": progresso_saldo,
                "texto": f"R$ {faltam:,.2f} restantes"
            })

        # Meta de meditação
        if self.total_meditado < 50:
            progresso_med = (self.total_meditado / 50) * 100
            faltam_med = 50 - self.total_meditado
            metas.append({
                "titulo": "🧘‍♀️ 50 Sessões de Meditação",
                "progresso": progresso_med,
                "texto": f"{faltam_med} sessões restantes"
            })

        # Exibir metas
        for meta in metas:
            self.criar_card_meta(metas_content, meta)

    def criar_card_meta(self, parent, meta):
        """Cria um card de meta"""
        meta_frame = tk.Frame(parent, bg=self.cores['card'])
        meta_frame.pack(fill='x', pady=10)

        # Título da meta
        tk.Label(
            meta_frame,
            text=meta['titulo'],
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(anchor='w')

        # Barra de progresso da meta
        progress_frame = tk.Frame(meta_frame, bg=self.cores['card'])
        progress_frame.pack(fill='x', pady=(5, 0))

        # Fundo da barra
        progress_bg = tk.Frame(
            progress_frame,
            bg=self.cores['progresso_bg'],
            height=8
        )
        progress_bg.pack(fill='x')

        # Preenchimento da barra
        largura_total = 300  # Largura fixa para cálculo
        largura_progresso = int((meta['progresso'] / 100) * largura_total)

        if largura_progresso > 0:
            progress_fill = tk.Frame(
                progress_bg,
                bg=self.cores['progresso'],
                width=largura_progresso,
                height=8
            )
            progress_fill.pack(side='left')

        # Texto do progresso
        tk.Label(
            meta_frame,
            text=f"{meta['progresso']:.1f}% - {meta['texto']}",
            font=self.fontes['texto_pequeno'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack(anchor='w', pady=(2, 0))

    def criar_aba_gratidao(self):
        aba_gratidao = tk.Frame(self.notebook, bg=self.cores['bg_principal'])
        self.notebook.add(aba_gratidao, text="🙏 Gratidão")
        
        # Título
        titulo = tk.Label(
            aba_gratidao,
            text="Diário da Gratidão",
            font=('Helvetica', 24, 'bold'),
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        )
        titulo.pack(pady=20)
        
        # Frame para o texto da gratidão
        frame_gratidao = tk.Frame(
            aba_gratidao,
            bg=self.cores['bg_principal']
        )
        frame_gratidao.pack(fill='both', expand=True, padx=20)
        
        # Texto fixo inicial
        texto_inicial = "Hoje sou feliz e grata pois..."
        self.texto_gratidao = tk.Text(
            frame_gratidao,
            height=5,
            width=50,
            font=('Helvetica', 12),
            wrap='word',
            bg=self.cores['card'],
            fg=self.cores['texto']
        )
        self.texto_gratidao.pack(pady=10)
        self.texto_gratidao.insert('1.0', texto_inicial)
        
        # Botão para salvar gratidão
        botao_salvar = tk.Button(
            frame_gratidao,
            text="💖 Salvar Gratidão",
            command=self.salvar_gratidao,
            font=('Helvetica', 12, 'bold'),
            bg=self.cores['botao'],
            fg=self.cores['texto_botao'],
            relief='flat',
            padx=20,
            pady=10
        )
        botao_salvar.pack(pady=10)
        
        # Lista de gratidões
        tk.Label(
            frame_gratidao,
            text="✨ Minhas Gratidões ✨",
            font=('Helvetica', 16, 'bold'),
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        ).pack(pady=(10, 5))
        
        # Frame para os botões (agora logo após o título)
        frame_botoes = tk.Frame(frame_gratidao, bg=self.cores['bg_principal'])
        frame_botoes.pack(fill='x', pady=(0, 5))
        
        # Botão para visualizar gratidão selecionada
        botao_visualizar = tk.Button(
            frame_botoes,
            text="👁️ Visualizar Gratidão",
            command=lambda: self.mostrar_gratidao_detalhada(None),
            font=('Helvetica', 12),
            bg=self.cores['botao'],
            fg=self.cores['texto_botao'],
            relief='flat',
            padx=20,
            pady=5
        )
        botao_visualizar.pack(side='left', padx=5)
        
        # Botão para excluir gratidão selecionada
        botao_excluir = tk.Button(
            frame_botoes,
            text="🗑️ Excluir Gratidão",
            command=self.excluir_gratidao,
            font=('Helvetica', 12),
            bg=self.cores['erro'],
            fg=self.cores['texto_botao'],
            relief='flat',
            padx=20,
            pady=5
        )
        botao_excluir.pack(side='right', padx=5)
        
        # Frame para lista com scroll
        frame_lista = tk.Frame(frame_gratidao, bg=self.cores['bg_principal'])
        frame_lista.pack(fill='both', expand=True)
        
        # Lista de gratidões com scroll
        self.lista_gratidoes = tk.Listbox(
            frame_lista,
            font=('Helvetica', 12),
            bg=self.cores['card'],
            fg=self.cores['texto'],
            selectmode='single',
            height=8,
            width=50
        )
        scrollbar = ttk.Scrollbar(frame_lista, orient="vertical", command=self.lista_gratidoes.yview)
        self.lista_gratidoes.configure(yscrollcommand=scrollbar.set)
        
        # Pack da lista e scrollbar
        self.lista_gratidoes.pack(side='left', fill='both', expand=True, pady=(0, 5))
        scrollbar.pack(side='right', fill='y', pady=(0, 5))
        
        # Bind para mostrar mensagem ao clicar
        self.lista_gratidoes.bind('<Double-Button-1>', self.mostrar_gratidao_detalhada)

    def excluir_gratidao(self):
        try:
            # Obter o índice da gratidão selecionada
            indice = self.lista_gratidoes.curselection()[0]
            
            # Remover a gratidão da lista
            self.lista_gratidoes.delete(indice)
            
            # Salvar logs atualizados
            self.salvar_logs()
            
            self.mostrar_mensagem(
                "🗑️ Gratidão Excluída",
                "A gratidão selecionada foi removida com sucesso!"
            )
        except IndexError:
            self.mostrar_mensagem(
                "⚠️ Atenção",
                "Por favor, selecione uma gratidão para excluir!"
            )

    def mostrar_gratidao_detalhada(self, event):
        try:
            indice = self.lista_gratidoes.curselection()[0]
            mensagem = self.lista_gratidoes.get(indice)
            self.mostrar_mensagem("✨ Gratidão Detalhada", mensagem)
        except IndexError:
            pass

    def criar_aba_investimentos(self):
        aba_investimentos = tk.Frame(
            self.notebook,
            bg=self.cores['bg_principal']
        )
        self.notebook.add(aba_investimentos, text="💰 Investimentos")

        # Usar a função centralizada de scroll
        scrollable_frame = self.criar_scroll_frame(aba_investimentos)

        # Lista de investimentos disponíveis
        self.investimentos = [
            {
                "nome": "Tesouro Universal",
                "descricao": "Investimento seguro com retorno garantido",
                "valor": 10000,
                "retorno": 1.15,
                "tempo": 5
            },
            {
                "nome": "Ações da Abundância",
                "descricao": "Alto risco, alto retorno",
                "valor": 50000,
                "retorno": 2.0,
                "tempo": 10
            },
            {
                "nome": "Fundo Manifestação",
                "descricao": "Equilibrado e consistente",
                "valor": 25000,
                "retorno": 1.5,
                "tempo": 7
            },
            {
                "nome": "Cripto Universal",
                "descricao": "Investimento em criptomoedas com alto potencial",
                "valor": 75000,
                "retorno": 2.5,
                "tempo": 15
            },
            {
                "nome": "Fundo Imobiliário",
                "descricao": "Investimento em imóveis de alto padrão",
                "valor": 100000,
                "retorno": 1.8,
                "tempo": 12
            },
            {
                "nome": "Startup Visionária",
                "descricao": "Investimento em startups promissoras",
                "valor": 150000,
                "retorno": 3.0,
                "tempo": 20
            }
        ]
        
        # Criar cards para cada investimento
        for inv in self.investimentos:
            self.criar_card_investimento(scrollable_frame, inv)

    def criar_card_investimento(self, container, investimento):
        card = tk.Frame(
            container,
            bg=self.cores['card'],
            padx=20,
            pady=20,
            relief='solid',
            borderwidth=1
        )
        card.pack(fill='x', pady=10, padx=20)
        
        # Nome do investimento
        tk.Label(
            card,
            text=investimento['nome'],
            font=self.fontes['subtitulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(anchor='w')
        
        # Descrição
        tk.Label(
            card,
            text=investimento['descricao'],
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto_secundario']
        ).pack(anchor='w', pady=(5,10))
        
        # Detalhes do investimento
        detalhes = tk.Frame(card, bg=self.cores['card'])
        detalhes.pack(fill='x')
        
        # Valor
        tk.Label(
            detalhes,
            text=f"Valor: R$ {investimento['valor']:,.2f}",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left', padx=10)
        
        # Retorno
        tk.Label(
            detalhes,
            text=f"Retorno: {(investimento['retorno']-1)*100:.0f}%",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left', padx=10)
        
        # Tempo
        tk.Label(
            detalhes,
            text=f"Tempo: {investimento['tempo']} dias",
            font=self.fontes['texto'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(side='left', padx=10)
        
        # Botão investir
        tk.Button(
            card,
            text="Investir",
            command=lambda: self.fazer_investimento(investimento),
            font=self.fontes['botao'],
            bg=self.cores['destaque'],
            fg=self.cores['bg_secundario'],
            relief='flat',
            padx=20,
            pady=5,
            cursor='hand2'
        ).pack(anchor='e', pady=(10,0))

    def fazer_investimento(self, investimento):
        data_atual = datetime.now()
        
        # Verificar se já investiu recentemente neste mesmo investimento
        for inv_id, inv_dados in self.investimentos_ativos.items():
            if inv_dados['nome'] == investimento['nome']:
                data_inicio = datetime.strptime(inv_dados['data_inicio'], '%Y-%m-%d %H:%M:%S')
                tempo_passado = (data_atual - data_inicio).days
                
                if tempo_passado < inv_dados['tempo']:
                    dias_restantes = inv_dados['tempo'] - tempo_passado
                    self.mostrar_mensagem(
                        "⏳ Aguarde",
                        f"Você precisa esperar mais {dias_restantes} dias para fazer um novo investimento em {investimento['nome']}."
                    )
                    return
        
        if self.saldo < investimento['valor']:
            self.mostrar_mensagem("Saldo Insuficiente", "Você não possui saldo suficiente para este investimento.")
            return
        
        # Realizar investimento
        self.saldo -= investimento['valor']
        self.investimentos_feitos += 1
        
        # Registrar investimento ativo
        investimento_id = f"inv_{len(self.investimentos_ativos) + 1}"
        self.investimentos_ativos[investimento_id] = {
            'nome': investimento['nome'],
            'valor': investimento['valor'],
            'retorno': investimento['retorno'],
            'tempo': investimento['tempo'],
            'data_inicio': data_atual.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.atualizar_interface()
        self.salvar_progresso()
        
        self.mostrar_mensagem(
            "💰 Investimento Realizado",
            f"Investimento em {investimento['nome']} realizado!\n\n"
            f"Valor investido: R$ {investimento['valor']:,.2f}\n"
            f"Tempo de retorno: {investimento['tempo']} dias\n"
            f"Retorno esperado: R$ {(investimento['valor'] * investimento['retorno']):,.2f}"
        )

    def criar_aba_surpresas(self):
        aba_surpresas = tk.Frame(
            self.notebook,
            bg=self.cores['bg_principal']
        )
        self.notebook.add(aba_surpresas, text="✨ Vida me Surpreenda")
        
        # Card principal
        card = tk.Frame(
            aba_surpresas,
            bg=self.cores['card'],
            padx=30,
            pady=30,
            relief='solid',
            borderwidth=1
        )
        card.pack(fill='both', expand=True, pady=20, padx=20)
        
        # Título
        tk.Label(
            card,
            text="Deixe a Vida te Surpreender",
            font=self.fontes['subtitulo'],
            bg=self.cores['card'],
            fg=self.cores['texto']
        ).pack(pady=(0,20))
        
        # Botão de surpresa
        tk.Button(
            card,
            text="Receber Surpresa do Universo",
            command=self.gerar_surpresa,
            font=self.fontes['botao'],
            bg=self.cores['destaque'],
            fg=self.cores['bg_secundario'],
            relief='flat',
            padx=20,
            pady=10
        ).pack(pady=20)

        # Frame para lista com scroll
        frame_lista = tk.Frame(card, bg=self.cores['card'])
        frame_lista.pack(fill='both', expand=True)
        
        # Lista de surpresas com scroll
        self.lista_surpresas = tk.Listbox(
            frame_lista,
            font=self.fontes['texto'],
            bg=self.cores['bg_secundario'],
            fg=self.cores['texto'],
            selectmode='single',
            height=8,
            width=50
        )
        scrollbar = ttk.Scrollbar(frame_lista, orient="vertical", command=self.lista_surpresas.yview)
        self.lista_surpresas.configure(yscrollcommand=scrollbar.set)
        
        # Pack da lista e scrollbar
        self.lista_surpresas.pack(side='left', fill='both', expand=True, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Bind para mostrar mensagem ao clicar
        self.lista_surpresas.bind('<<ListboxSelect>>', self.mostrar_surpresa_detalhada)

    def mostrar_surpresa_detalhada(self, event):
        try:
            indice = self.lista_surpresas.curselection()[0]
            mensagem = self.lista_surpresas.get(indice)
            self.mostrar_mensagem("✨ Surpresa Detalhada", mensagem)
        except IndexError:
            pass

    def gerar_surpresa(self):
        # Gerar número aleatório para determinar o tipo de surpresa
        numero = random.random()  # Gera número entre 0 e 1
        
        if numero < 0.20:  # 20% de chance para loteria
            if not self.loteria_sorteada:  # Verifica se a loteria já foi sorteada hoje
                self.gerar_loteria()
                self.loteria_sorteada = True  # Marca que a loteria foi sorteada
                return
        
        # Distribuir as outras probabilidades
        if numero < 0.2:  # 20% para carros
            tipo = 'carro'
            item = random.choice(self.surpresas['carros'])
            mensagem = f"Seu novo carro: {item}"
            bonus = random.randint(50000, 200000)
            
        elif numero < 0.4:  # 20% para experiências
            tipo = 'experiencia'
            item = random.choice(self.surpresas['experiencias'])
            mensagem = f"Experiência: {item}"
            bonus = random.randint(10000, 50000)
            
        elif numero < 0.7:  # 30% para destinos
            tipo = 'destino'
            destino = random.choice(self.surpresas['destinos'])
            mensagem = f"Viagem para {destino['ponto_turistico']} em {destino['cidade']}, {destino['pais']}!\n\n{destino['descricao']}"
            bonus = random.randint(20000, 100000)
            
        else:  # 49.9% para dinheiro
            tipo = 'dinheiro'
            valor = random.randint(50000, 500000)
            mensagem = f"Presente em dinheiro: R$ {valor:,.2f}"
            bonus = valor
        
        data_atual = datetime.now().strftime("%d/%m/%Y %H:%M")
        self.lista_surpresas.insert(0, f"{data_atual} - {mensagem}")
        
        # Adicionar XP baseado no tipo de surpresa
        xp_ganho = {
            'carro': 200,
            'experiencia': 100,
            'destino': 150,
            'dinheiro': 50
        }.get(tipo, 50)
        
        self.saldo += bonus
        self.xp += xp_ganho
        self.atualizar_interface()
        self.salvar_progresso()
        self.salvar_logs()  # Salvar logs após adicionar surpresa
        
        self.mostrar_mensagem("✨ Surpresa do Universo", f"{mensagem}\n\nBônus recebido:\nR$ {bonus:,.2f}\n+{xp_ganho} XP")

    def gerar_loteria(self):
        modalidade = random.choice(list(self.modalidades_loteria.keys()))  # Escolhe uma modalidade aleatória
        quantidade = self.modalidades_loteria[modalidade]['quantidade']
        max_numero = self.modalidades_loteria[modalidade]['max_numero']
        numeros_sorteados = random.sample(range(1, max_numero + 1), quantidade)  # Gera números aleatórios
        mensagem = f"Modalidade: {modalidade}\nNúmeros sorteados: {sorted(numeros_sorteados)}"
        self.mostrar_mensagem("Resultado da Loteria", mensagem)

    def criar_aba_presentes(self):
        aba_presentes = tk.Frame(
            self.notebook,
            bg=self.cores['bg_principal']
        )
        self.notebook.add(aba_presentes, text="🎁 Presentes do Universo")
        
        # Título
        titulo = tk.Label(
            aba_presentes,
            text="🎁 Presentes do Universo 🎁",
            font=('Helvetica', 24, 'bold'),
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        )
        titulo.pack(pady=20)
        
        # Descrição
        descricao = tk.Label(
            aba_presentes,
            text="O Universo sempre tem presentes especiais reservados para você.\nCada presente é uma manifestação da abundância infinita!",
            font=('Helvetica', 12),
            bg=self.cores['bg_principal'],
            fg=self.cores['texto_secundario'],
            justify='center'
        )
        descricao.pack(pady=10)
        
        # Botão para receber presente
        botao_presente = tk.Button(
            aba_presentes,
            text="🎁 Receber Presente do Universo",
            font=('Helvetica', 14, 'bold'),
            command=self.receber_presente,
            bg=self.cores['botao'],
            fg=self.cores['texto_botao'],
            relief='flat',
            padx=20,
            pady=10
        )
        botao_presente.pack(pady=20)
        
        # Frame para lista de presentes recebidos
        frame_lista = tk.Frame(
            aba_presentes,
            bg=self.cores['bg_principal']
        )
        frame_lista.pack(fill='both', expand=True, padx=20)
        
        # Título da lista
        tk.Label(
            frame_lista,
            text="✨ Presentes Recebidos ✨",
            font=('Helvetica', 16, 'bold'),
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        ).pack(pady=10)
        
        # Lista de presentes com scroll
        self.lista_presentes = tk.Listbox(
            frame_lista,
            font=('Helvetica', 12),
            bg=self.cores['card'],
            fg=self.cores['texto'],
            selectmode='single',
            height=10,
            width=50
        )
        scrollbar = ttk.Scrollbar(frame_lista, orient="vertical", command=self.lista_presentes.yview)
        self.lista_presentes.configure(yscrollcommand=scrollbar.set)
        
        # Pack da lista e scrollbar
        self.lista_presentes.pack(side='left', fill='both', expand=True, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
        # Bind para mostrar mensagem ao clicar
        self.lista_presentes.bind('<<ListboxSelect>>', self.mostrar_presente_detalhado)

    def mostrar_presente_detalhado(self, event):
        try:
            indice = self.lista_presentes.curselection()[0]
            mensagem = self.lista_presentes.get(indice)
            self.mostrar_mensagem("🎁 Presente Detalhado", mensagem)
        except IndexError:
            pass

    def receber_presente(self):
        # Verificar se já recebeu presente hoje
        hoje = datetime.now().date()
        ultima_data = self.carregar_ultima_data_presente()
        
        if ultima_data == hoje:
            self.mostrar_mensagem(
                "✨ Presente do Universo",
                "Você já recebeu seu presente hoje!\nVolte amanhã para mais bênçãos! 🙏"
            )
            return
        
        # Selecionar presente aleatório
        presente = random.choice(self.presentes_universo)
        
        # Adicionar timestamp
        timestamp = datetime.now().strftime("%d/%m/%Y %H:%M")
        presente_com_data = f"{timestamp} - {presente}"
        
        # Adicionar à lista
        self.lista_presentes.insert(0, presente_com_data)
        
        # Salvar data do presente
        self.salvar_ultima_data_presente(hoje)
        
        # Recompensar usuário
        bonus = random.randint(1000, 10000)
        self.saldo += bonus
        self.xp += 50
        
        # Mostrar mensagem usando o método padrão
        self.mostrar_mensagem(
            "✨ Presente do Universo",
            f"{presente}\n\nBônus: R$ {bonus:,.2f}\nXP: +50"
        )
        
        self.atualizar_interface()
        self.salvar_progresso()
        self.salvar_logs()  # Salvar logs após adicionar presente

    def carregar_ultima_data_presente(self):
        if hasattr(self, 'ultima_data_presente'):
            return datetime.strptime(self.ultima_data_presente, "%Y-%m-%d").date()
        return None

    def salvar_ultima_data_presente(self, data):
        self.ultima_data_presente = data.strftime("%Y-%m-%d")

    def criar_aba_conquistas(self):
        aba_conquistas = tk.Frame(self.notebook, bg=self.cores['bg_principal'])
        self.notebook.add(aba_conquistas, text="🏆 Conquistas")
        
        # Título com efeito de brilho
        titulo_frame = tk.Frame(aba_conquistas, bg=self.cores['bg_principal'])
        titulo_frame.pack(pady=20)
        
        tk.Label(
            titulo_frame,
            text="✨",
            font=('Helvetica', 30),
            bg=self.cores['bg_principal'],
            fg=self.cores['destaque']
        ).pack(side='left')
        
        tk.Label(
            titulo_frame,
            text="Suas Conquistas Especiais",
            font=('Helvetica', 24, 'bold'),
            bg=self.cores['bg_principal'],
            fg=self.cores['texto']
        ).pack(side='left')
        
        tk.Label(
            titulo_frame,
            text="✨",
            font=('Helvetica', 30),
            bg=self.cores['bg_principal'],
            fg=self.cores['destaque']
        ).pack(side='left')
        
        # Container para os cards com scroll
        container = tk.Frame(aba_conquistas, bg=self.cores['bg_principal'])
        container.pack(fill='both', expand=True, padx=20)

        # Usar a função centralizada de scroll
        scrollable_frame = self.criar_scroll_frame(container)
        
        # Criar card para cada conquista
        for conquista_id, conquista in self.conquistas.items():
            # Determinar nível da conquista
            nivel = 'bronze'
            if 'elite' in conquista_id or 'mestre' in conquista_id:
                nivel = 'diamante'
            elif 'avancado' in conquista_id:
                nivel = 'ouro'
            elif 'intermediario' in conquista_id:
                nivel = 'prata'
            
            # Cores e efeitos do nível
            cores = self.cores_conquistas[nivel]
            efeitos = self.efeitos_conquistas[nivel]
            
            # Card principal com efeito de brilho
            card = tk.Frame(
                scrollable_frame,
                bg=cores['bg'],
                relief='raised',
                borderwidth=3
            )
            card.pack(fill='x', pady=10, padx=20)
            
            # Efeito de brilho no topo
            brilho = tk.Label(
                card,
                text=efeitos['brilho'] * 5,
                font=('Helvetica', 20),
                bg=cores['bg'],
                fg=cores['brilho']
            )
            brilho.pack(fill='x')
            
            # Badge e título
            header = tk.Frame(card, bg=cores['bg'])
            header.pack(fill='x', pady=10)
            
            # Badge com efeito de partículas
            badge_frame = tk.Frame(header, bg=cores['bg'])
            badge_frame.pack(side='left', padx=20)
            
            tk.Label(
                badge_frame,
                text=efeitos['particulas'],
                font=('Helvetica', 20),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack()
            
            tk.Label(
                badge_frame,
                text=efeitos['icone'],
                font=('Helvetica', 40),
                bg=cores['bg']
            ).pack()
            
            tk.Label(
                badge_frame,
                text=efeitos['particulas'],
                font=('Helvetica', 20),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack()
            
            # Nome da conquista
            tk.Label(
                header,
                text=conquista['nome'],
                font=('Helvetica', 16, 'bold'),
                bg=cores['bg'],
                fg=cores['texto']
            ).pack(side='left')
            
            # Informações
            info_frame = tk.Frame(card, bg=cores['bg'])
            info_frame.pack(fill='x', padx=20, pady=5)
            
            # Descrição
            tk.Label(
                info_frame,
                text=conquista['descricao'],
                font=('Helvetica', 12),
                bg=cores['bg'],
                fg=cores['texto']
            ).pack(anchor='w')
            
            # Recompensa
            tk.Label(
                info_frame,
                text=f"💰 Recompensa: R$ {conquista['recompensa']:,.2f}",
                font=('Helvetica', 12, 'bold'),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack(anchor='w', pady=5)
            
            # Status com animação
            status_frame = tk.Frame(info_frame, bg=cores['bg'])
            status_frame.pack(anchor='e', pady=5)
            
            if conquista['desbloqueada']:
                status_text = f"{efeitos['brilho']} CONQUISTADO {efeitos['brilho']}"
                status_color = cores['brilho']
            else:
                status_text = "🔒 BLOQUEADO"
                status_color = cores['texto']
            
            tk.Label(
                status_frame,
                text=status_text,
                font=('Helvetica', 12, 'bold'),
                bg=cores['bg'],
                fg=status_color
            ).pack()
            
            # Efeito de brilho no rodapé
            brilho = tk.Label(
                card,
                text=efeitos['brilho'] * 5,
                font=('Helvetica', 20),
                bg=cores['bg'],
                fg=cores['brilho']
            )
            brilho.pack(fill='x')
            
            # Efeitos de hover
            def on_enter(e, card=card, cores=cores):
                card.configure(relief='solid', borderwidth=5)
                for widget in card.winfo_children():
                    if isinstance(widget, tk.Label):
                        widget.configure(fg=cores['brilho'])
            
            def on_leave(e, card=card, cores=cores):
                card.configure(relief='raised', borderwidth=3)
                for widget in card.winfo_children():
                    if isinstance(widget, tk.Label):
                        widget.configure(fg=cores['texto'])
            
            card.bind('<Enter>', on_enter)
            card.bind('<Leave>', on_leave)

    def desbloquear_conquista(self, conquista_id):
        if conquista_id in self.conquistas and not self.conquistas[conquista_id]['desbloqueada']:
            conquista = self.conquistas[conquista_id]
            conquista['desbloqueada'] = True

            # Adicionar recompensa
            self.saldo += conquista['recompensa']
            self.xp += 100

            # Ganhar moedas especiais por conquista
            moedas_ganhas = random.randint(5, 15)
            self.moedas_especiais += moedas_ganhas

            # Criar notificação de conquista
            self.criar_notificacao(
                f"🏆 Conquista Desbloqueada!",
                f"{conquista['nome']}\n💰 R$ {conquista['recompensa']:,.2f} + 💎 {moedas_ganhas}",
                tipo="sucesso",
                duracao=8000
            )
            
            # Determinar nível da conquista
            nivel = 'bronze'
            if 'elite' in conquista_id or 'mestre' in conquista_id:
                nivel = 'diamante'
            elif 'avancado' in conquista_id:
                nivel = 'ouro'
            elif 'intermediario' in conquista_id:
                nivel = 'prata'
            
            # Cores e efeitos do nível
            cores = self.cores_conquistas[nivel]
            efeitos = self.efeitos_conquistas[nivel]
            
            # Criar janela de animação
            anim_window = tk.Toplevel(self)
            anim_window.overrideredirect(True)  # Remove bordas da janela
            anim_window.attributes('-topmost', True)  # Mantém no topo
            
            # Centralizar a janela
            largura = 400
            altura = 300
            x = self.winfo_screenwidth()//2 - largura//2
            y = self.winfo_screenheight()//2 - altura//2
            anim_window.geometry(f'{largura}x{altura}+{x}+{y}')
            
            # Frame principal
            main_frame = tk.Frame(anim_window, bg=cores['bg'])
            main_frame.pack(expand=True, fill='both')
            
            # Efeito de brilho no topo
            tk.Label(
                main_frame,
                text=efeitos['brilho'] * 10,
                font=('Helvetica', 30),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack(pady=10)
            
            # Ícone da conquista
            tk.Label(
                main_frame,
                text=efeitos['icone'],
                font=('Helvetica', 60),
                bg=cores['bg']
            ).pack(pady=10)
            
            # Partículas
            tk.Label(
                main_frame,
                text=efeitos['particulas'] * 5,
                font=('Helvetica', 30),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack()
            
            # Título
            tk.Label(
                main_frame,
                text="🏆 CONQUISTA DESBLOQUEADA! 🏆",
                font=('Helvetica', 20, 'bold'),
                bg=cores['bg'],
                fg=cores['texto']
            ).pack(pady=10)
            
            # Nome da conquista
            tk.Label(
                main_frame,
                text=conquista['nome'],
                font=('Helvetica', 16),
                bg=cores['bg'],
                fg=cores['texto']
            ).pack()
            
            # Recompensa
            tk.Label(
                main_frame,
                text=f"💰 R$ {conquista['recompensa']:,.2f}",
                font=('Helvetica', 14, 'bold'),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack(pady=5)
            
            # XP
            tk.Label(
                main_frame,
                text=f"✨ +100 XP",
                font=('Helvetica', 14),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack()
            
            # Efeito de brilho no rodapé
            tk.Label(
                main_frame,
                text=efeitos['brilho'] * 10,
                font=('Helvetica', 30),
                bg=cores['bg'],
                fg=cores['brilho']
            ).pack(pady=10)
            
            # Fechar a janela após 3 segundos
            self.after(3000, anim_window.destroy)
            
            # Atualizar interface
            self.atualizar_interface()
            self.salvar_progresso()

    def ativar_afirmacao(self, afirmacao):
        bonus = random.randint(5000, 25000)
        xp_ganho = random.randint(10, 30)

        self.saldo += bonus
        self.xp += xp_ganho
        self.afirmacoes_ativadas += 1

        # Ganhar moedas especiais ocasionalmente
        bonus_moedas_msg = ""
        if random.random() < 0.3:  # 30% de chance
            moedas_ganhas = random.randint(1, 5)
            self.moedas_especiais += moedas_ganhas
            bonus_moedas_msg = f"\n💎 +{moedas_ganhas} Cristais Universais!"

        # Verificar conquistas e nível
        self.verificar_conquistas()
        self.verificar_nivel()
        self.atualizar_interface()

        # Salvar progresso
        self.salvar_progresso()

        self.mostrar_mensagem("✨ Afirmação Ativada", f"Você ativou a afirmação:\n{afirmacao}\n\n"
            f"Bônus recebido:\n💰 R$ {bonus:,.2f}\n"
            f"⭐ +{xp_ganho} XP{bonus_moedas_msg}")

    def verificar_conquistas(self):
        # Sistema de conquistas
        conquistas_verificar = [
            # Conquistas de Manifestação Financeira
            ('manifestador_iniciante', self.saldo >= 100000),
            ('manifestador_expert', self.saldo >= 1000000),
            ('manifestador_mestre', self.saldo >= 10000000),
            ('manifestador_elite', self.saldo >= 100000000),
            
            # Conquistas de Gratidão
            ('gratidao_iniciante', self.gratidoes_registradas >= 10),
            ('gratidao_intermediario', self.gratidoes_registradas >= 50),
            ('gratidao_avancado', self.gratidoes_registradas >= 100),
            ('gratidao_mestre', self.gratidoes_registradas >= 500),
            
            # Conquistas de Visualização
            ('visualizador_iniciante', self.visualizacoes_hoje >= 10),
            ('visualizador_intermediario', self.visualizacoes_hoje >= 50),
            ('visualizador_avancado', self.visualizacoes_hoje >= 100),
            ('visualizador_pro', self.visualizacoes_hoje >= 500),
            
            # Conquistas de Meditação
            ('meditador_iniciante', self.tempo_total_meditado >= 3600),  # 1 hora
            ('meditador_intermediario', self.tempo_total_meditado >= 18000),  # 5 horas
            ('meditador_avancado', self.tempo_total_meditado >= 36000),  # 10 horas
            ('mestre_zen', self.tempo_total_meditado >= 180000),  # 50 horas
            
            # Conquistas de Investimentos
            ('investidor_iniciante', self.investimentos_feitos >= 5),
            ('investidor_intermediario', self.investimentos_feitos >= 20),
            ('investidor_avancado', self.investimentos_feitos >= 50),
            ('investidor_mestre', self.investimentos_feitos >= 100),
            
            # Conquistas de Nível
            ('nivel_5', self.nivel >= 5),
            ('nivel_10', self.nivel >= 10),
            ('nivel_20', self.nivel >= 20),
            ('nivel_50', self.nivel >= 50),
            
            # Conquistas de Surpresas
            ('surpresa_iniciante', hasattr(self, 'lista_surpresas') and self.lista_surpresas.size() >= 10),
            ('surpresa_intermediario', hasattr(self, 'lista_surpresas') and self.lista_surpresas.size() >= 50),
            ('surpresa_avancado', hasattr(self, 'lista_surpresas') and self.lista_surpresas.size() >= 100),
            ('surpresa_mestre', hasattr(self, 'lista_surpresas') and self.lista_surpresas.size() >= 500),
            
            # Conquistas de Presentes
            ('presente_iniciante', hasattr(self, 'lista_presentes') and self.lista_presentes.size() >= 10),
            ('presente_intermediario', hasattr(self, 'lista_presentes') and self.lista_presentes.size() >= 50),
            ('presente_avancado', hasattr(self, 'lista_presentes') and self.lista_presentes.size() >= 100),
            ('presente_mestre', hasattr(self, 'lista_presentes') and self.lista_presentes.size() >= 500),
            
            # Conquistas de Afirmações
            ('afirmacao_iniciante', hasattr(self, 'afirmacoes_ativadas') and self.afirmacoes_ativadas >= 10),
            ('afirmacao_intermediario', hasattr(self, 'afirmacoes_ativadas') and self.afirmacoes_ativadas >= 50),
            ('afirmacao_avancado', hasattr(self, 'afirmacoes_ativadas') and self.afirmacoes_ativadas >= 100),
            ('afirmacao_mestre', hasattr(self, 'afirmacoes_ativadas') and self.afirmacoes_ativadas >= 500)
        ]
        
        for conquista_id, condicao in conquistas_verificar:
            if condicao and not self.conquistas[conquista_id]['desbloqueada']:
                self.desbloquear_conquista(conquista_id)

    def manifestar(self):
        valor = random.randint(10000, 100000)
        xp_ganho = random.randint(20, 50)
        
        self.saldo += valor
        self.xp += xp_ganho
        
        # Verificar nível antes de atualizar interface
        self.verificar_nivel()
        self.atualizar_interface()
        
        self.mostrar_mensagem("✨ Manifestação", f"Você manifestou R$ {valor:,.2f}!\n+{xp_ganho} XP")

    def visualizar(self):
        self.visualizacoes_hoje += 1
        bonus = random.randint(5000, 25000)
        self.saldo += bonus
        self.xp += 30
        self.atualizar_interface()
        self.salvar_progresso()
        self.verificar_conquistas()
        
        self.mostrar_mensagem("👁️ Visualização", f"Visualização concluída!\nBônus: R$ {bonus:,.2f}\n+30 XP")

    def gratidao(self):
        texto = "Por favor, registre sua gratidão na aba específica de Gratidão."
        self.mostrar_mensagem("🙏 Gratidão", texto)
        # Mudar para a aba de gratidão
        self.notebook.select(2)  # índice da aba de gratidão

    def investir(self):
        texto = "Por favor, acesse a aba de Investimentos para ver as opções disponíveis."
        self.mostrar_mensagem("💰 Investimento", texto)
        # Mudar para a aba de investimentos
        self.notebook.select(3)  # índice da aba de investimentos

    def verificar_nivel(self):
        # Debugar valores antes
        print(f"XP atual: {self.xp}, XP necessário: {self.xp_proximo_nivel}, Nível atual: {self.nivel}")
        
        while self.xp >= self.xp_proximo_nivel:
            # Subir de nível
            self.nivel += 1
            self.xp = self.xp - self.xp_proximo_nivel
            self.xp_proximo_nivel = int(self.xp_proximo_nivel * 1.5)
            
            # Bônus por nível
            bonus = self.nivel * 50000
            self.saldo += bonus
            
            # Atualizar interface imediatamente
            self.nivel_label.config(text=f"Nível {self.nivel}")
            self.xp_label.config(text=f"XP: {self.xp}/{self.xp_proximo_nivel}")
            self.progress['value'] = (self.xp / self.xp_proximo_nivel) * 100
            
            # Forçar atualização visual
            self.update_idletasks()
            
            # Notificar usuário
            self.mostrar_mensagem("🎉 Nível Aumentado!", f"Parabéns! Você alcançou o nível {self.nivel}!\n"
                f"Bônus: R$ {bonus:,.2f}")
        
        # Debugar valores depois
        print(f"Após verificação - XP: {self.xp}, XP necessário: {self.xp_proximo_nivel}, Nível: {self.nivel}")

    def mostrar_mensagem(self, titulo, mensagem):
        # Desativar som do sistema
        self.option_add('*Bell', 'off')
        self.option_add('*MessageBox.bell', 'off')
        self.option_add('*Dialog.bell', 'off')
        self.option_add('*Toplevel.bell', 'off')

        # Criar janela de mensagem com dimensões responsivas
        msg_window = tk.Toplevel(self)
        msg_window.title(titulo)
        msg_window.configure(bg=self.cores['bg_principal'])
        msg_window.grab_set()  # Torna a janela modal
        msg_window.resizable(True, True)

        # Calcular dimensões baseadas na resolução
        if self.modo_nano:
            largura = min(300, int(self.screen_width * 0.9))
            altura = min(200, int(self.screen_height * 0.7))
            font_titulo = ('Segoe UI', 10, 'bold')
            font_texto = ('Segoe UI', 8)
            font_botao = ('Segoe UI', 8, 'bold')
            padding = 8
            wraplength = largura - 40
        elif self.modo_ultra_compacto:
            largura = min(400, int(self.screen_width * 0.85))
            altura = min(300, int(self.screen_height * 0.75))
            font_titulo = ('Segoe UI', 12, 'bold')
            font_texto = ('Segoe UI', 9)
            font_botao = ('Segoe UI', 9, 'bold')
            padding = 12
            wraplength = largura - 50
        else:
            largura, altura = self.config_responsiva["janela_popup"]
            font_titulo = self.fontes['subtitulo']
            font_texto = self.fontes['texto']
            font_botao = self.fontes['botao']
            padding = self.obter_padding_responsivo(20)
            wraplength = largura - 60

        # Centralizar a janela
        self.centralizar_janela(msg_window, largura, altura)

        # Container principal
        main_frame = tk.Frame(msg_window, bg=self.cores['bg_principal'])
        main_frame.pack(fill='both', expand=True, padx=padding, pady=padding)

        # Título
        titulo_label = tk.Label(
            main_frame,
            text=titulo,
            font=font_titulo,
            bg=self.cores['bg_principal'],
            fg=self.cores['dourado'],
            relief='flat',
            bd=0
        )
        titulo_label.pack(pady=(0, 10))

        # Mensagem
        mensagem_label = tk.Label(
            main_frame,
            text=mensagem,
            font=font_texto,
            bg=self.cores['bg_principal'],
            fg=self.cores['texto'],
            wraplength=wraplength,
            justify='center',
            relief='flat',
            bd=0
        )
        mensagem_label.pack(pady=(0, 15))
        
        # Botão OK responsivo
        botao_ok = tk.Button(
            main_frame,
            text="OK",
            command=msg_window.destroy,
            font=font_botao,
            bg=self.cores['sucesso'],
            fg=self.cores['texto_botao'],
            relief='flat',
            padx=20 if self.modo_nano else 30,
            pady=5 if self.modo_nano else 10,
            cursor='hand2',
            bd=0
        )
        botao_ok.pack()

        # Função para fechar a janela
        def fechar_janela(_=None):
            msg_window.destroy()

        # Bind das teclas
        msg_window.bind('<Escape>', fechar_janela)
        msg_window.bind('<Return>', fechar_janela)

        # Focar na janela
        msg_window.focus_set()

    def verificar_retornos_investimentos(self):
        data_atual = datetime.now()
        investimentos_concluidos = []
        
        for investimento_id, dados in self.investimentos_ativos.items():
            try:
                data_inicio = datetime.strptime(dados['data_inicio'], '%Y-%m-%d %H:%M:%S')
                tempo_passado = (data_atual - data_inicio).days
                
                if tempo_passado >= dados['tempo']:
                    # Calcular retorno
                    retorno = dados['valor'] * dados['retorno']
                    self.saldo += retorno
                    self.xp += 100
                    
                    # Adicionar à lista de concluídos
                    investimentos_concluidos.append(investimento_id)
                    
                    # Mostrar mensagem de retorno
                    self.mostrar_mensagem(
                        "💰 Retorno de Investimento",
                        f"Seu investimento em {dados['nome']} foi concluído!\n\n"
                        f"Valor investido: R$ {dados['valor']:,.2f}\n"
                        f"Retorno: R$ {retorno:,.2f}\n"
                        f"Lucro: R$ {(retorno - dados['valor']):,.2f}\n"
                        f"XP ganho: 100"
                    )
            except ValueError as e:
                print(f"Erro na conversão da data: {e}")
                continue  # Pula para o próximo investimento se houver erro
        
        # Remover investimentos concluídos
        for investimento_id in investimentos_concluidos:
            del self.investimentos_ativos[investimento_id]
        
        # Atualizar interface e salvar progresso
        self.atualizar_interface()
        self.salvar_progresso()

    def executar(self):
        self.mainloop()

if __name__ == "__main__":
    app = JogoLeiAtracao()
    app.executar()