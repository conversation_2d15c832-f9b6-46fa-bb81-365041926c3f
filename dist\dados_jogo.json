{"nome_usuario": "<PERSON><PERSON>", "saldo": 64019354.0, "nivel": 11, "xp": 2579, "xp_proximo_nivel": 5743, "ultima_loteria": "2025-03-27", "modalidade_sorteada": "Lotofácil", "conquistas": {"manifestador_iniciante": {"nome": "Manifestador Iniciante", "badge": "🥉", "descricao": "Alcance R$ 100.000", "recompensa": 10000, "desbloqueada": true}, "manifestador_expert": {"nome": "Manifestador Expert", "badge": "🥈", "descricao": "Alcance R$ 1.000.000", "recompensa": 50000, "desbloqueada": true}, "manifestador_mestre": {"nome": "Manifestador Mestre", "badge": "🥇", "descricao": "Alcance R$ 10.000.000", "recompensa": 100000, "desbloqueada": false}, "manifestador_elite": {"nome": "Manifestador Elite", "badge": "👑", "descricao": "Alcance R$ 100.000.000", "recompensa": 500000, "desbloqueada": false}, "gratidao_iniciante": {"nome": "Gratidão Iniciante", "badge": "🙏", "descricao": "Registre 10 gratidões", "recompensa": 5000, "desbloqueada": false}, "gratidao_intermediario": {"nome": "Gratidão Intermediário", "badge": "🙏🙏", "descricao": "Registre 50 gratidões", "recompensa": 10000, "desbloqueada": false}, "gratidao_avancado": {"nome": "<PERSON><PERSON><PERSON><PERSON>", "badge": "🙏🙏🙏", "descricao": "Registre 100 gratidões", "recompensa": 25000, "desbloqueada": false}, "gratidao_mestre": {"nome": "Me<PERSON><PERSON> da Gratidão", "badge": "🌟", "descricao": "Registre 500 gratidões", "recompensa": 100000, "desbloqueada": false}, "visualizador_iniciante": {"nome": "Visualizador <PERSON>", "badge": "👁️", "descricao": "Complete 10 visualizações", "recompensa": 5000, "desbloqueada": false}, "visualizador_intermediario": {"nome": "Visualizador Intermediário", "badge": "👁️👁️", "descricao": "Complete 50 visualizações", "recompensa": 10000, "desbloqueada": false}, "visualizador_avancado": {"nome": "<PERSON><PERSON><PERSON>", "badge": "👁️👁️👁️", "descricao": "Complete 100 visualizações", "recompensa": 25000, "desbloqueada": false}, "visualizador_pro": {"nome": "Visualizador Profissional", "badge": "💎", "descricao": "Complete 500 visualizações", "recompensa": 200000, "desbloqueada": false}, "meditador_iniciante": {"nome": "Meditador Iniciante", "badge": "🧘", "descricao": "Medite por 1 hora no total", "recompensa": 5000, "desbloqueada": false}, "meditador_intermediario": {"nome": "Meditador Intermediário", "badge": "🧘🧘", "descricao": "Medite por 5 horas no total", "recompensa": 10000, "desbloqueada": false}, "meditador_avancado": {"nome": "Meditador Avançado", "badge": "🧘🧘🧘", "descricao": "Medite por 10 horas no total", "recompensa": 25000, "desbloqueada": false}, "mestre_zen": {"nome": "Mestre Zen", "badge": "👑", "descricao": "Medite por 50 horas no total", "recompensa": 500000, "desbloqueada": false}, "investidor_iniciante": {"nome": "Investidor <PERSON>ici<PERSON>", "badge": "💰", "descricao": "Faça 5 investimentos", "recompensa": 5000, "desbloqueada": false}, "investidor_intermediario": {"nome": "Investidor Intermediário", "badge": "💰💰", "descricao": "Faça 20 investimentos", "recompensa": 10000, "desbloqueada": false}, "investidor_avancado": {"nome": "Investi<PERSON>", "badge": "💰💰💰", "descricao": "Faça 50 investimentos", "recompensa": 25000, "desbloqueada": false}, "investidor_mestre": {"nome": "Mestre dos Investimentos", "badge": "💎", "descricao": "Faça 100 investimentos", "recompensa": 100000, "desbloqueada": false}, "nivel_5": {"nome": "Iniciante Promissor", "badge": "⭐", "descricao": "Alcance o nível 5", "recompensa": 5000, "desbloqueada": true}, "nivel_10": {"nome": "Manifestador em Ascensão", "badge": "⭐⭐", "descricao": "Alcance o nível 10", "recompensa": 10000, "desbloqueada": false}, "nivel_20": {"nome": "Manifestador Experiente", "badge": "⭐⭐⭐", "descricao": "Alcance o nível 20", "recompensa": 25000, "desbloqueada": false}, "nivel_50": {"nome": "Manifestador Elite", "badge": "👑", "descricao": "Alcance o nível 50", "recompensa": 100000, "desbloqueada": false}, "surpresa_iniciante": {"nome": "Surpresas Iniciais", "badge": "🎁", "descricao": "Receba 10 surpresas", "recompensa": 5000, "desbloqueada": false}, "surpresa_intermediario": {"nome": "Surpresas Intermediárias", "badge": "🎁🎁", "descricao": "Receba 50 surpresas", "recompensa": 10000, "desbloqueada": false}, "surpresa_avancado": {"nome": "Surpresas Avançadas", "badge": "🎁🎁🎁", "descricao": "Receba 100 surpresas", "recompensa": 25000, "desbloqueada": false}, "surpresa_mestre": {"nome": "Mestre das Surpresas", "badge": "💫", "descricao": "Receba 500 surpresas", "recompensa": 100000, "desbloqueada": false}, "presente_iniciante": {"nome": "Presentes Iniciais", "badge": "🎀", "descricao": "Receba 10 presentes", "recompensa": 5000, "desbloqueada": false}, "presente_intermediario": {"nome": "Presentes Intermediá<PERSON>s", "badge": "🎀🎀", "descricao": "Receba 50 presentes", "recompensa": 10000, "desbloqueada": false}, "presente_avancado": {"nome": "Presentes <PERSON>", "badge": "🎀🎀🎀", "descricao": "Receba 100 presentes", "recompensa": 25000, "desbloqueada": false}, "presente_mestre": {"nome": "Mestre dos Presentes", "badge": "✨", "descricao": "Receba 500 presentes", "recompensa": 100000, "desbloqueada": false}, "afirmacao_iniciante": {"nome": "Afirmações Iniciais", "badge": "💫", "descricao": "Ative 10 afirmações", "recompensa": 5000, "desbloqueada": false}, "afirmacao_intermediario": {"nome": "Afirmações Intermediárias", "badge": "💫💫", "descricao": "Ative 50 afirmações", "recompensa": 10000, "desbloqueada": false}, "afirmacao_avancado": {"nome": "Afirmações Avançadas", "badge": "💫💫💫", "descricao": "Ative 100 afirmações", "recompensa": 25000, "desbloqueada": false}, "afirmacao_mestre": {"nome": "Mestre das Afirmações", "badge": "🌟", "descricao": "Ative 500 afirmações", "recompensa": 100000, "desbloqueada": false}}, "visualizacoes_hoje": 0, "total_meditado": 0, "tempo_total_meditado": 0, "investimentos_feitos": 6, "gratidoes_registradas": 0, "afirmacoes_ativadas": 69, "ultimo_investimento": "2025-03-27 03:06:23", "investimentos_ativos": {"inv_1": {"nome": "Tesouro Universal", "valor": 10000, "retorno": 1.15, "tempo": 5, "data_inicio": "2025-03-27 03:06:23"}, "inv_2": {"nome": "Ações da Abundância", "valor": 50000, "retorno": 2.0, "tempo": 10, "data_inicio": "2025-03-27 03:10:39"}, "inv_3": {"nome": "Fundo Manifestação", "valor": 25000, "retorno": 1.5, "tempo": 7, "data_inicio": "2025-03-27 03:10:44"}, "inv_4": {"nome": "Cripto Universal", "valor": 75000, "retorno": 2.5, "tempo": 15, "data_inicio": "2025-03-27 03:10:47"}, "inv_5": {"nome": "Fundo Imobiliário", "valor": 100000, "retorno": 1.8, "tempo": 12, "data_inicio": "2025-03-27 03:10:50"}, "inv_6": {"nome": "Startup <PERSON>", "valor": 150000, "retorno": 3.0, "tempo": 20, "data_inicio": "2025-03-27 03:10:53"}}}